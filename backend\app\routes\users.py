from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import schemas
from app.utils.db import get_db
from app.core.dependencies import get_current_user, get_current_active_user
from app.services import user_service

router = APIRouter()


@router.get("/me", response_model=schemas.User)
async def get_current_user_info(
    current_user: schemas.User = Depends(get_current_user),
) -> Any:
    """
    Get current user information
    """
    return current_user


@router.put("/me", response_model=schemas.User)
async def update_current_user(
    *,
    db: Session = Depends(get_db),
    user_in: schemas.UserUpdate,
    current_user: schemas.User = Depends(get_current_active_user),
) -> Any:
    """
    Update current user information
    """
    user = user_service.update_user(db=db, user=current_user, obj_in=user_in)
    return user


@router.get("/profile", response_model=schemas.User)
async def get_user_profile(
    current_user: schemas.User = Depends(get_current_user),
) -> Any:
    """
    Get user profile (alias for /me)
    """
    return current_user


@router.put("/profile", response_model=schemas.User)
async def update_user_profile(
    *,
    db: Session = Depends(get_db),
    user_in: schemas.UserUpdate,
    current_user: schemas.User = Depends(get_current_active_user),
) -> Any:
    """
    Update user profile (alias for /me)
    """
    user = user_service.update_user(db=db, user=current_user, obj_in=user_in)
    return user
