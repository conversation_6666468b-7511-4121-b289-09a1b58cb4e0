{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://saasservicemgmt.googleapis.com/", "batchPath": "batch", "canonicalName": "SaaS Service Management", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/saas-runtime/docs", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "saasservicemgmt:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://saasservicemgmt.mtls.googleapis.com/", "name": "saasservicemgmt", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudLocationLocation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1beta1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"releases": {"methods": {"create": {"description": "Create a new release.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/releases", "httpMethod": "POST", "id": "saasservicemgmt.projects.locations.releases.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent of the release.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "releaseId": {"description": "Required. The ID value for the new release.", "location": "query", "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/releases", "request": {"$ref": "Release"}, "response": {"$ref": "Release"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single release.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/releases/{releasesId}", "httpMethod": "DELETE", "id": "saasservicemgmt.projects.locations.releases.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "The etag known to the client for the expected state of the release. This is used with state-changing methods to prevent accidental overwrites when multiple user agents might be acting in parallel on the same resource. An etag wildcard provide optimistic concurrency based on the expected existence of the release. The Any wildcard (`*`) requires that the resource must already exists, and the Not Any wildcard (`!*`) requires that it must not.", "location": "query", "type": "string"}, "name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/releases/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieve a single release.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/releases/{releasesId}", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.releases.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/releases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Release"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Retrieve a collection of releases.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/releases", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.releases.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter the list as specified in https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Order results as specified in https://google.aip.dev/132.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of releases to send per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token: If the next_page_token from a previous response is provided, this request will send the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the release.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/releases", "response": {"$ref": "ListReleasesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single release.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/releases/{releasesId}", "httpMethod": "PATCH", "id": "saasservicemgmt.projects.locations.releases.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/releases/{release}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/releases/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Field mask is used to specify the fields to be overwritten in the Release resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields in the Release will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Release"}, "response": {"$ref": "Release"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "rolloutKinds": {"methods": {"create": {"description": "Create a new rollout kind.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/rolloutKinds", "httpMethod": "POST", "id": "saasservicemgmt.projects.locations.rolloutKinds.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent of the rollout kind.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "rolloutKindId": {"description": "Required. The ID value for the new rollout kind.", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/rolloutKinds", "request": {"$ref": "RolloutKind"}, "response": {"$ref": "RolloutKind"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single rollout kind.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/rolloutKinds/{rolloutKindsId}", "httpMethod": "DELETE", "id": "saasservicemgmt.projects.locations.rolloutKinds.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "The etag known to the client for the expected state of the rollout kind. This is used with state-changing methods to prevent accidental overwrites when multiple user agents might be acting in parallel on the same resource. An etag wildcard provide optimistic concurrency based on the expected existence of the rollout kind. The Any wildcard (`*`) requires that the resource must already exists, and the Not Any wildcard (`!*`) requires that it must not.", "location": "query", "type": "string"}, "name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/rolloutKinds/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieve a single rollout kind.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/rolloutKinds/{rolloutKindsId}", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.rolloutKinds.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/rolloutKinds/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "RolloutKind"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Retrieve a collection of rollout kinds.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/rolloutKinds", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.rolloutKinds.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter the list as specified in https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Order results as specified in https://google.aip.dev/132.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of rollout kinds to send per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token: If the next_page_token from a previous response is provided, this request will send the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the rollout kind.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/rolloutKinds", "response": {"$ref": "ListRolloutKindsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single rollout kind.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/rolloutKinds/{rolloutKindsId}", "httpMethod": "PATCH", "id": "saasservicemgmt.projects.locations.rolloutKinds.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/rolloutKinds/{rollout_kind_id}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/rolloutKinds/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Field mask is used to specify the fields to be overwritten in the RolloutKind resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields in the RolloutKind will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "request": {"$ref": "RolloutKind"}, "response": {"$ref": "RolloutKind"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "rollouts": {"methods": {"create": {"description": "Create a new rollout.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/rollouts", "httpMethod": "POST", "id": "saasservicemgmt.projects.locations.rollouts.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent of the rollout.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "rolloutId": {"description": "Required. The ID value for the new rollout.", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/rollouts", "request": {"$ref": "Rollout"}, "response": {"$ref": "Rollout"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single rollout.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/rollouts/{rolloutsId}", "httpMethod": "DELETE", "id": "saasservicemgmt.projects.locations.rollouts.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "The etag known to the client for the expected state of the rollout. This is used with state-changing methods to prevent accidental overwrites when multiple user agents might be acting in parallel on the same resource. An etag wildcard provide optimistic concurrency based on the expected existence of the rollout. The Any wildcard (`*`) requires that the resource must already exists, and the Not Any wildcard (`!*`) requires that it must not.", "location": "query", "type": "string"}, "name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/rollouts/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieve a single rollout.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/rollouts/{rolloutsId}", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.rollouts.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/rollouts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Rollout"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Retrieve a collection of rollouts.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/rollouts", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.rollouts.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter the list as specified in https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Order results as specified in https://google.aip.dev/132.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of rollouts to send per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token: If the next_page_token from a previous response is provided, this request will send the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the rollout.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/rollouts", "response": {"$ref": "ListRolloutsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single rollout.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/rollouts/{rolloutsId}", "httpMethod": "PATCH", "id": "saasservicemgmt.projects.locations.rollouts.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/rollout/{rollout_id}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/rollouts/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Field mask is used to specify the fields to be overwritten in the Rollout resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields in the Rollout will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Rollout"}, "response": {"$ref": "Rollout"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "saas": {"methods": {"create": {"description": "Create a new saas.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/saas", "httpMethod": "POST", "id": "saasservicemgmt.projects.locations.saas.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent of the saas.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "saasId": {"description": "Required. The ID value for the new saas.", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/saas", "request": {"$ref": "Saas"}, "response": {"$ref": "Saas"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single saas.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/saas/{saasId}", "httpMethod": "DELETE", "id": "saasservicemgmt.projects.locations.saas.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "The etag known to the client for the expected state of the saas. This is used with state-changing methods to prevent accidental overwrites when multiple user agents might be acting in parallel on the same resource. An etag wildcard provide optimistic concurrency based on the expected existence of the saas. The Any wildcard (`*`) requires that the resource must already exists, and the Not Any wildcard (`!*`) requires that it must not.", "location": "query", "type": "string"}, "name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/saas/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieve a single saas.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/saas/{saasId}", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.saas.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/saas/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Saas"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Retrieve a collection of saas.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/saas", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.saas.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter the list as specified in https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Order results as specified in https://google.aip.dev/132.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of saas to send per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token: If the next_page_token from a previous response is provided, this request will send the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the saas.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/saas", "response": {"$ref": "ListSaasResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single saas.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/saas/{saasId}", "httpMethod": "PATCH", "id": "saasservicemgmt.projects.locations.saas.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/saas/{saas}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/saas/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Field mask is used to specify the fields to be overwritten in the Saas resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields in the Saas will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Saas"}, "response": {"$ref": "Saas"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "tenants": {"methods": {"create": {"description": "Create a new tenant.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tenants", "httpMethod": "POST", "id": "saasservicemgmt.projects.locations.tenants.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent of the tenant.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "tenantId": {"description": "Required. The ID value for the new tenant.", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/tenants", "request": {"$ref": "Tenant"}, "response": {"$ref": "Tenant"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single tenant.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tenants/{tenantsId}", "httpMethod": "DELETE", "id": "saasservicemgmt.projects.locations.tenants.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "The etag known to the client for the expected state of the tenant. This is used with state-changing methods to prevent accidental overwrites when multiple user agents might be acting in parallel on the same resource. An etag wildcard provide optimistic concurrency based on the expected existence of the tenant. The Any wildcard (`*`) requires that the resource must already exists, and the Not Any wildcard (`!*`) requires that it must not.", "location": "query", "type": "string"}, "name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tenants/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieve a single tenant.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tenants/{tenantsId}", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.tenants.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tenants/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Tenant"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Retrieve a collection of tenants.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tenants", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.tenants.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter the list as specified in https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Order results as specified in https://google.aip.dev/132.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of tenants to send per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token: If the next_page_token from a previous response is provided, this request will send the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the tenant.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/tenants", "response": {"$ref": "ListTenantsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single tenant.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tenants/{tenantsId}", "httpMethod": "PATCH", "id": "saasservicemgmt.projects.locations.tenants.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/tenants/{tenant}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tenants/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Field mask is used to specify the fields to be overwritten in the Tenant resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields in the Tenant will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Tenant"}, "response": {"$ref": "Tenant"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "unitKinds": {"methods": {"create": {"description": "Create a new unit kind.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/unitKinds", "httpMethod": "POST", "id": "saasservicemgmt.projects.locations.unitKinds.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent of the unit kind.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "unitKindId": {"description": "Required. The ID value for the new unit kind.", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/unitKinds", "request": {"$ref": "UnitKind"}, "response": {"$ref": "UnitKind"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single unit kind.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/unitKinds/{unitKindsId}", "httpMethod": "DELETE", "id": "saasservicemgmt.projects.locations.unitKinds.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "The etag known to the client for the expected state of the unit kind. This is used with state-changing methods to prevent accidental overwrites when multiple user agents might be acting in parallel on the same resource. An etag wildcard provide optimistic concurrency based on the expected existence of the unit kind. The Any wildcard (`*`) requires that the resource must already exists, and the Not Any wildcard (`!*`) requires that it must not.", "location": "query", "type": "string"}, "name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/unitKinds/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieve a single unit kind.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/unitKinds/{unitKindsId}", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.unitKinds.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/unitKinds/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "UnitKind"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Retrieve a collection of unit kinds.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/unitKinds", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.unitKinds.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter the list as specified in https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Order results as specified in https://google.aip.dev/132.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of unit kinds to send per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token: If the next_page_token from a previous response is provided, this request will send the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the unit kind.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/unitKinds", "response": {"$ref": "ListUnitKindsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single unit kind.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/unitKinds/{unitKindsId}", "httpMethod": "PATCH", "id": "saasservicemgmt.projects.locations.unitKinds.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/unitKinds/{unitKind}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/unitKinds/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Field mask is used to specify the fields to be overwritten in the UnitKind resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields in the UnitKind will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "request": {"$ref": "UnitKind"}, "response": {"$ref": "UnitKind"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "unitOperations": {"methods": {"create": {"description": "Create a new unit operation.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/unitOperations", "httpMethod": "POST", "id": "saasservicemgmt.projects.locations.unitOperations.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent of the unit operation.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "unitOperationId": {"description": "Required. The ID value for the new unit operation.", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/unitOperations", "request": {"$ref": "UnitOperation"}, "response": {"$ref": "UnitOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single unit operation.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/unitOperations/{unitOperationsId}", "httpMethod": "DELETE", "id": "saasservicemgmt.projects.locations.unitOperations.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "The etag known to the client for the expected state of the unit operation. This is used with state-changing methods to prevent accidental overwrites when multiple user agents might be acting in parallel on the same resource. An etag wildcard provide optimistic concurrency based on the expected existence of the unit operation. The Any wildcard (`*`) requires that the resource must already exists, and the Not Any wildcard (`!*`) requires that it must not.", "location": "query", "type": "string"}, "name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/unitOperations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieve a single unit operation.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/unitOperations/{unitOperationsId}", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.unitOperations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/unitOperations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "UnitOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Retrieve a collection of unit operations.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/unitOperations", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.unitOperations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter the list as specified in https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Order results as specified in https://google.aip.dev/132.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of unit operations to send per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token: If the next_page_token from a previous response is provided, this request will send the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the unit operation.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/unitOperations", "response": {"$ref": "ListUnitOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single unit operation.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/unitOperations/{unitOperationsId}", "httpMethod": "PATCH", "id": "saasservicemgmt.projects.locations.unitOperations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/unitOperations/{unitOperation}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/unitOperations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Field mask is used to specify the fields to be overwritten in the UnitOperation resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields in the UnitOperation will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "request": {"$ref": "UnitOperation"}, "response": {"$ref": "UnitOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "units": {"methods": {"create": {"description": "Create a new unit.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/units", "httpMethod": "POST", "id": "saasservicemgmt.projects.locations.units.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent of the unit.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "unitId": {"description": "Required. The ID value for the new unit.", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/units", "request": {"$ref": "Unit"}, "response": {"$ref": "Unit"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete a single unit.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/units/{unitsId}", "httpMethod": "DELETE", "id": "saasservicemgmt.projects.locations.units.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "The etag known to the client for the expected state of the unit. This is used with state-changing methods to prevent accidental overwrites when multiple user agents might be acting in parallel on the same resource. An etag wildcard provide optimistic concurrency based on the expected existence of the unit. The Any wildcard (`*`) requires that the resource must already exists, and the Not Any wildcard (`!*`) requires that it must not.", "location": "query", "type": "string"}, "name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/units/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieve a single unit.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/units/{unitsId}", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.units.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the resource within a service.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/units/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Unit"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Retrieve a collection of units.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/units", "httpMethod": "GET", "id": "saasservicemgmt.projects.locations.units.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter the list as specified in https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Order results as specified in https://google.aip.dev/132.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of units to send per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token: If the next_page_token from a previous response is provided, this request will send the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the unit.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/units", "response": {"$ref": "ListUnitsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single unit.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/units/{unitsId}", "httpMethod": "PATCH", "id": "saasservicemgmt.projects.locations.units.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/units/{unit}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/units/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Field mask is used to specify the fields to be overwritten in the Unit resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields in the Unit will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "If \"validate_only\" is set to true, the service will try to validate that this request would succeed, but will not actually make changes.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Unit"}, "response": {"$ref": "Unit"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250528", "rootUrl": "https://saasservicemgmt.googleapis.com/", "schemas": {"Aggregate": {"description": "Represents the aggregation of a set of population of like records by a certain group. For example, a collection of unit counts can be aggregated and grouped by their state.", "id": "Aggregate", "properties": {"count": {"description": "Required. Number of records in the group.", "format": "int32", "type": "integer"}, "group": {"description": "Required. Group by which to aggregate.", "type": "string"}}, "type": "object"}, "Blueprint": {"description": "Blueprints are OCI Images that contain all of the artifacts needed to provision a unit. Metadata such as, type of the engine used to actuate the blueprint (e.g. terraform, helm etc) and version will come from the image manifest. If the hostname is omitted, it will be assumed to be the regional path to Artifact Registry (eg. us-east1-docker.pkg.dev).", "id": "Blueprint", "properties": {"engine": {"description": "Output only. Type of the engine used to actuate the blueprint. e.g. terraform, helm etc.", "readOnly": true, "type": "string"}, "package": {"description": "Optional. Immutable. URI to a blueprint used by the Unit (required unless unitKind or release is set).", "type": "string"}, "version": {"description": "Output only. Version metadata if present on the blueprint.", "readOnly": true, "type": "string"}}, "type": "object"}, "Dependency": {"description": "Dependency represent a single dependency with another unit kind by alias.", "id": "Dependency", "properties": {"alias": {"description": "Required. An alias for the dependency. Used for input variable mapping.", "type": "string"}, "unitKind": {"description": "Required. Immutable. The unit kind of the dependency.", "type": "string"}}, "type": "object"}, "Deprovision": {"description": "Deprovision is the unit operation that deprovision the underlying resources represented by a Unit. Can only execute if the Unit is currently provisioned.", "id": "Deprovision", "properties": {}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "ErrorBudget": {"description": "The configuration for error budget. If the number of failed units exceeds max(allowed_count, allowed_ratio * total_units), the rollout will be paused.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"allowedCount": {"description": "Optional. The maximum number of failed units allowed in a location without pausing the rollout.", "format": "int32", "type": "integer"}, "allowedPercentage": {"description": "Optional. The maximum percentage of units allowed to fail (0, 100] within a location without pausing the rollout.", "format": "int32", "type": "integer"}}, "type": "object"}, "FromMapping": {"description": "Output variables whose values will be passed on to dependencies", "id": "FromMapping", "properties": {"dependency": {"description": "Required. <PERSON><PERSON> of the dependency that the outputVariable will pass its value to", "type": "string"}, "outputVariable": {"description": "Required. Name of the outputVariable on the dependency", "type": "string"}}, "type": "object"}, "GoogleCloudLocationLocation": {"description": "A resource that represents a Google Cloud location.", "id": "GoogleCloudLocationLocation", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "GoogleCloudLocationLocation"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListReleasesResponse": {"description": "The response structure for the ListReleases method.", "id": "ListReleasesResponse", "properties": {"nextPageToken": {"description": "If present, the next page token can be provided to a subsequent ListReleases call to list the next page. If empty, there are no more pages.", "type": "string"}, "releases": {"description": "The resulting releases.", "items": {"$ref": "Release"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListRolloutKindsResponse": {"description": "The response structure for the ListRolloutKinds method.", "id": "ListRolloutKindsResponse", "properties": {"nextPageToken": {"description": "If present, the next page token can be provided to a subsequent ListRolloutKinds call to list the next page. If empty, there are no more pages.", "type": "string"}, "rolloutKinds": {"description": "The resulting rollout kinds.", "items": {"$ref": "RolloutKind"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListRolloutsResponse": {"description": "The response structure for the ListRollouts method.", "id": "ListRolloutsResponse", "properties": {"nextPageToken": {"description": "If present, the next page token can be provided to a subsequent ListRollouts call to list the next page. If empty, there are no more pages.", "type": "string"}, "rollouts": {"description": "The resulting rollouts.", "items": {"$ref": "Rollout"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListSaasResponse": {"description": "The response structure for the ListSaas method.", "id": "ListSaasResponse", "properties": {"nextPageToken": {"description": "If present, the next page token can be provided to a subsequent ListSaas call to list the next page. If empty, there are no more pages.", "type": "string"}, "saas": {"description": "The resulting saas.", "items": {"$ref": "Saas"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListTenantsResponse": {"description": "The response structure for the ListTenants method.", "id": "ListTenantsResponse", "properties": {"nextPageToken": {"description": "If present, the next page token can be provided to a subsequent ListTenants call to list the next page. If empty, there are no more pages.", "type": "string"}, "tenants": {"description": "The resulting tenants.", "items": {"$ref": "Tenant"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListUnitKindsResponse": {"description": "The response structure for the ListUnitKinds method.", "id": "ListUnitKindsResponse", "properties": {"nextPageToken": {"description": "If present, the next page token can be provided to a subsequent ListUnitKinds call to list the next page. If empty, there are no more pages.", "type": "string"}, "unitKinds": {"description": "The resulting unit kinds.", "items": {"$ref": "UnitKind"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListUnitOperationsResponse": {"description": "The response structure for the ListUnitOperations method.", "id": "ListUnitOperationsResponse", "properties": {"nextPageToken": {"description": "If present, the next page token can be provided to a subsequent ListUnitOperations call to list the next page. If empty, there are no more pages.", "type": "string"}, "unitOperations": {"description": "The resulting unit operations.", "items": {"$ref": "UnitOperation"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListUnitsResponse": {"description": "The response structure for the ListUnits method.", "id": "ListUnitsResponse", "properties": {"nextPageToken": {"description": "If present, the next page token can be provided to a subsequent ListUnits call to list the next page. If empty, there are no more pages.", "type": "string"}, "units": {"description": "The resulting units.", "items": {"$ref": "Unit"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Location": {"description": "Location information that the service is available in.", "id": "Location", "properties": {"name": {"description": "Optional. Name of location.", "type": "string"}}, "type": "object"}, "MaintenanceSettings": {"description": "Captures requested directives for performing future maintenance on the unit. This includes a request for the unit to skip maintenance for a period of time and remain pinned to its current release as well as controls for postponing maintenance scheduled in future.", "id": "MaintenanceSettings", "properties": {"pinnedUntilTime": {"description": "Optional. If present, it fixes the release on the unit until the given time; i.e. changes to the release field will be rejected. Rollouts should and will also respect this by not requesting an upgrade in the first place.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Provision": {"description": "Provision is the unit operation that provision the underlying resources represented by a Unit. Can only execute if the Unit is not currently provisioned.", "id": "Provision", "properties": {"inputVariables": {"description": "Optional. Set of input variables. Maximum 100. (optional)", "items": {"$ref": "UnitVariable"}, "type": "array"}, "release": {"description": "Optional. Reference to the Release object to use for the Unit. (optional).", "type": "string"}}, "type": "object"}, "Release": {"description": "A new version to be propagated and deployed to units. This includes pointers to packaged blueprints for actuation (e.g Helm or Terraform configuration packages) via artifact registry.", "id": "Release", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Annotations is an unstructured key-value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/user-guide/annotations", "type": "object"}, "blueprint": {"$ref": "Blueprint", "description": "Optional. Blueprints are OCI Images that contain all of the artifacts needed to provision a unit."}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. An opaque value that uniquely identifies a version or generation of a resource. It can be used to confirm that the client and server agree on the ordering of a resource being written.", "readOnly": true, "type": "string"}, "inputVariableDefaults": {"description": "Optional. Mapping of input variables to default values. Maximum 100", "items": {"$ref": "UnitVariable"}, "type": "array"}, "inputVariables": {"description": "Optional. Output only. List of input variables declared on the blueprint and can be present with their values on the unit spec", "items": {"$ref": "UnitVariable"}, "readOnly": true, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels on the resource, which can be used for categorization. similar to Kubernetes resource labels.", "type": "object"}, "name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/releases/{release}\"", "type": "string"}, "outputVariables": {"description": "Optional. Output only. List of output variables declared on the blueprint and can be present with their values on the unit status", "items": {"$ref": "UnitVariable"}, "readOnly": true, "type": "array"}, "releaseRequirements": {"$ref": "ReleaseRequirements", "description": "Optional. Set of requirements to be fulfilled on the Unit when using this Release."}, "uid": {"description": "Output only. The unique identifier of the resource. UID is unique in the time and space for this resource within the scope of the service. It is typically generated by the server on successful creation of a resource and must not be changed. UID is used to uniquely identify resources with resource name reuses. This should be a UUID4.", "readOnly": true, "type": "string"}, "unitKind": {"description": "Required. Immutable. Reference to the UnitKind this Release corresponds to (required and immutable once created).", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was last updated. Any change to the resource made by users must refresh this value. Changes to a resource made by the service should refresh this value.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ReleaseRequirements": {"description": "Set of requirements to be fulfilled on the Unit when using this Release.", "id": "ReleaseRequirements", "properties": {"upgradeableFromReleases": {"description": "Optional. A list of releases from which a unit can be upgraded to this one (optional). If left empty no constraints will be applied. When provided, unit upgrade requests to this release will check and enforce this constraint.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Rollout": {"description": "Represents a single rollout execution and its results", "id": "Rollout", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Annotations is an unstructured key-value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/user-guide/annotations", "type": "object"}, "control": {"$ref": "RolloutControl", "description": "Optional. Requested change to the execution of this rollout. Default RolloutControl.action is ROLLOUT_ACTION_RUN meaning the rollout will be executed to completion while progressing through all natural Rollout States (such as RUNNING -> SUCCEEDED or RUNNING -> FAILED). Requests can only be made when the Rollout is in a non-terminal state."}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Optional. Output only. The time when the rollout finished execution (regardless of success, failure, or cancellation). Will be empty if the rollout hasn't finished yet. Once set, the rollout is in terminal state and all the results are final.", "format": "google-datetime", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. An opaque value that uniquely identifies a version or generation of a resource. It can be used to confirm that the client and server agree on the ordering of a resource being written.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels on the resource, which can be used for categorization. similar to Kubernetes resource labels.", "type": "object"}, "name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/rollout/{rollout_id}\"", "type": "string"}, "parentRollout": {"description": "Optional. Output only. The direct parent rollout that this rollout is stemming from. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/rollouts/{rollout_id}\"", "readOnly": true, "type": "string"}, "release": {"description": "Optional. Immutable. Name of the Release that gets rolled out to target Units. Required if no other type of release is specified.", "type": "string"}, "rolloutKind": {"description": "Optional. Immutable. Name of the RolloutKind this rollout is stemming from and adhering to.", "type": "string"}, "rolloutOrchestrationStrategy": {"description": "Optional. The strategy used for executing this Rollout. This strategy will override whatever strategy is specified in the RolloutType. If not specified on creation, the strategy from RolloutType will be used. There are two supported values strategies which are used to control - \"Google.Cloud.Simple.AllAtOnce\" - \"Google.Cloud.Simple.OneLocationAtATime\" A rollout with one of these simple strategies will rollout across all locations defined in the targeted UnitKind's Saas Locations.", "type": "string"}, "rootRollout": {"description": "Optional. Output only. The root rollout that this rollout is stemming from. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/rollouts/{rollout_id}\"", "readOnly": true, "type": "string"}, "startTime": {"description": "Optional. Output only. The time when the rollout started executing. Will be empty if the rollout hasn't started yet.", "format": "google-datetime", "readOnly": true, "type": "string"}, "state": {"description": "Output only. Current state of the rollout.", "enum": ["ROLLOUT_STATE_UNSPECIFIED", "ROLLOUT_STATE_RUNNING", "ROLLOUT_STATE_PAUSED", "ROLLOUT_STATE_SUCCEEDED", "ROLLOUT_STATE_FAILED", "ROLLOUT_STATE_CANCELLED", "ROLLOUT_STATE_WAITING", "ROLLOUT_STATE_CANCELLING", "ROLLOUT_STATE_RESUMING", "ROLLOUT_STATE_PAUSING"], "enumDescriptions": ["Unspecified state.", "Rollout is in progress.", "Rollout has been paused.", "Rollout completed successfully.", "Rollout has failed.", "Rollout has been canceled.", "Rollout is waiting for some condition to be met before starting.", "Rollout is being canceled.", "Rollout is being resumed.", "Rollout is being paused."], "readOnly": true, "type": "string"}, "stateMessage": {"description": "Output only. Human readable message indicating details about the last state transition.", "readOnly": true, "type": "string"}, "stateTransitionTime": {"description": "Optional. Output only. The time when the rollout transitioned into its current state.", "format": "google-datetime", "readOnly": true, "type": "string"}, "stats": {"$ref": "RolloutStats", "description": "Optional. Output only. Details about the progress of the rollout.", "readOnly": true}, "uid": {"description": "Output only. The unique identifier of the resource. UID is unique in the time and space for this resource within the scope of the service. It is typically generated by the server on successful creation of a resource and must not be changed. UID is used to uniquely identify resources with resource name reuses. This should be a UUID4.", "readOnly": true, "type": "string"}, "unitFilter": {"description": "Optional. CEL(https://github.com/google/cel-spec) formatted filter string against Unit. The filter will be applied to determine the eligible unit population. This filter can only reduce, but not expand the scope of the rollout. If not provided, the unit_filter from the RolloutType will be used.", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was last updated. Any change to the resource made by users must refresh this value. Changes to a resource made by the service should refresh this value.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "RolloutControl": {"description": "RolloutControl provides a way to request a change to the execution of a Rollout by pausing or canceling it.", "id": "RolloutControl", "properties": {"action": {"description": "Required. Action to be performed on the Rollout. The default behavior is to run the rollout until it naturally reaches a terminal state.", "enum": ["ROLLOUT_ACTION_UNSPECIFIED", "ROLLOUT_ACTION_RUN", "ROLLOUT_ACTION_PAUSE", "ROLLOUT_ACTION_CANCEL"], "enumDescriptions": ["Unspecified action, will be treated as RUN by default.", "Run the Rollout until it naturally reaches a terminal state. A rollout requested to run will progress through all natural Rollout States (such as RUNNING -> SUCCEEDED or RUNNING -> FAILED). If retriable errors are encountered during the rollout, the rollout will paused by default and can be resumed by re-requesting this RUN action.", "Pause the Rollout until it is resumed (i.e. RUN is requested).", "Cancel the Rollout permanently."], "type": "string"}, "runParams": {"$ref": "RunRolloutActionParams", "description": "Optional. Parameters for the RUN action. It is an error to specify this if the RolloutAction is not set to RUN. By default, the rollout will retry failed operations when resumed."}}, "type": "object"}, "RolloutKind": {"description": "An object that describes various settings of Rollout execution. Includes built-in policies across GCP and GDC, and customizable policies.", "id": "RolloutKind", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Annotations is an unstructured key-value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/user-guide/annotations", "type": "object"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "errorBudget": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Optional. The configuration for error budget. If the number of failed units exceeds max(allowed_count, allowed_ratio * total_units), the rollout will be paused. If not set, all units will be attempted to be updated regardless of the number of failures encountered."}, "etag": {"description": "Output only. An opaque value that uniquely identifies a version or generation of a resource. It can be used to confirm that the client and server agree on the ordering of a resource being written.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels on the resource, which can be used for categorization. similar to Kubernetes resource labels.", "type": "object"}, "name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/rolloutKinds/{rollout_kind_id}\"", "type": "string"}, "rolloutOrchestrationStrategy": {"description": "Optional. The strategy used for executing a Rollout. This is a required field. There are two supported values strategies which are used to control - \"Google.Cloud.Simple.AllAtOnce\" - \"Google.Cloud.Simple.OneLocationAtATime\" A rollout with one of these simple strategies will rollout across all locations defined in the associated UnitKind's Saas Locations.", "type": "string"}, "uid": {"description": "Output only. The unique identifier of the resource. UID is unique in the time and space for this resource within the scope of the service. It is typically generated by the server on successful creation of a resource and must not be changed. UID is used to uniquely identify resources with resource name reuses. This should be a UUID4.", "readOnly": true, "type": "string"}, "unitFilter": {"description": "Optional. CEL(https://github.com/google/cel-spec) formatted filter string against Unit. The filter will be applied to determine the eligible unit population. This filter can only reduce, but not expand the scope of the rollout.", "type": "string"}, "unitKind": {"description": "Required. Immutable. UnitKind that this rollout kind corresponds to. Rollouts stemming from this rollout kind will target the units of this unit kind. In other words, this defines the population of target units to be upgraded by rollouts.", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was last updated. Any change to the resource made by users must refresh this value. Changes to a resource made by the service should refresh this value.", "format": "google-datetime", "readOnly": true, "type": "string"}, "updateUnitKindStrategy": {"description": "Optional. The config for updating the unit kind. By default, the unit kind will be updated on the rollout start.", "enum": ["UPDATE_UNIT_KIND_STRATEGY_UNSPECIFIED", "UPDATE_UNIT_KIND_STRATEGY_ON_START", "UPDATE_UNIT_KIND_STRATEGY_NEVER"], "enumDescriptions": ["Strategy unspecified.", "Update the unit kind strategy on the rollout start.", "Never update the unit kind."], "type": "string"}}, "type": "object"}, "RolloutStats": {"description": "RolloutStats contains information about the progress of a rollout.", "id": "RolloutStats", "properties": {"operationsByState": {"description": "Output only. A breakdown of the progress of operations triggered by the rollout. Provides a count of Operations by their state. This can be used to determine the number of units which have been updated, or are scheduled to be updated. There will be at most one entry per group. Possible values for operation groups are: - \"SCHEDULED\" - \"PENDING\" - \"RUNNING\" - \"SUCCEEDED\" - \"FAILED\" - \"CANCELLED\"", "items": {"$ref": "Aggregate"}, "readOnly": true, "type": "array"}}, "type": "object"}, "RunRolloutActionParams": {"description": "Parameters for the RUN action controlling the behavior of the rollout when it is resumed from a PAUSED state.", "id": "RunRolloutActionParams", "properties": {"retryFailedOperations": {"description": "Required. If true, the rollout will retry failed operations when resumed. This is applicable only the current state of the Rollout is PAUSED and the requested action is RUN.", "type": "boolean"}}, "type": "object"}, "Saas": {"description": "Saas is a representation of a SaaS service managed by the Producer.", "id": "Saas", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Annotations is an unstructured key-value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/user-guide/annotations", "type": "object"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. An opaque value that uniquely identifies a version or generation of a resource. It can be used to confirm that the client and server agree on the ordering of a resource being written.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels on the resource, which can be used for categorization. similar to Kubernetes resource labels.", "type": "object"}, "locations": {"description": "Optional. Immutable. List of locations that the service is available in. Rollout refers to the list to generate a rollout plan.", "items": {"$ref": "Location"}, "type": "array"}, "name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/saas/{saas}\"", "type": "string"}, "uid": {"description": "Output only. The unique identifier of the resource. UID is unique in the time and space for this resource within the scope of the service. It is typically generated by the server on successful creation of a resource and must not be changed. UID is used to uniquely identify resources with resource name reuses. This should be a UUID4.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was last updated. Any change to the resource made by users must refresh this value. Changes to a resource made by the service should refresh this value.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Schedule": {"description": "A time specification to schedule the maintenance.", "id": "Schedule", "properties": {"startTime": {"description": "Optional. Start of operation. If not set, will be set to the start of the next window. (optional)", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Tenant": {"description": "Tenant represents the service producer side of an instance of the service created based on a request from a consumer. In a typical scenario a Tenant has a one-to-one mapping with a resource given out to a service consumer. Example: tenant: name: \"projects/svc1/locations/loc/tenants/inst-068afff8\" consumer_resource: \"projects/gshoe/locations/loc/shoes/black-shoe\"", "id": "Tenant", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Annotations is an unstructured key-value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/user-guide/annotations", "type": "object"}, "consumerResource": {"description": "Optional. Immutable. A reference to the consumer resource this SaaS Tenant is representing. The relationship with a consumer resource can be used by EasySaaS for retrieving consumer-defined settings and policies such as maintenance policies (using Unified Maintenance Policy API).", "type": "string"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. An opaque value that uniquely identifies a version or generation of a resource. It can be used to confirm that the client and server agree on the ordering of a resource being written.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels on the resource, which can be used for categorization. similar to Kubernetes resource labels.", "type": "object"}, "name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/tenants/{tenant}\"", "type": "string"}, "saas": {"description": "Required. Immutable. A reference to the Saas that defines the product (managed service) that the producer wants to manage with EasySaaS. Part of the EasySaaS common data model.", "type": "string"}, "uid": {"description": "Output only. The unique identifier of the resource. UID is unique in the time and space for this resource within the scope of the service. It is typically generated by the server on successful creation of a resource and must not be changed. UID is used to uniquely identify resources with resource name reuses. This should be a UUID4.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was last updated. Any change to the resource made by users must refresh this value. Changes to a resource made by the service should refresh this value.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ToMapping": {"description": "Input variables whose values will be passed on to dependencies", "id": "ToMapping", "properties": {"dependency": {"description": "Required. <PERSON><PERSON> of the dependency that the inputVariable will pass its value to", "type": "string"}, "ignoreForLookup": {"description": "Optional. Tells EasySaaS if this mapping should be used during lookup or not", "type": "boolean"}, "inputVariable": {"description": "Required. Name of the inputVariable on the dependency", "type": "string"}}, "type": "object"}, "Unit": {"description": "A unit of deployment that has its lifecycle via a CRUD API using an actuation engine under the hood (e.g. based on Terraform, Helm or a custom implementation provided by a service producer). A building block of a SaaS Tenant.", "id": "Unit", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Annotations is an unstructured key-value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/user-guide/annotations", "type": "object"}, "conditions": {"description": "Optional. Output only. A set of conditions which indicate the various conditions this resource can have.", "items": {"$ref": "UnitCondition"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dependencies": {"description": "Optional. Output only. Set of dependencies for this unit. Maximum 10.", "items": {"$ref": "UnitDependency"}, "readOnly": true, "type": "array"}, "dependents": {"description": "Optional. Output only. List of Units that depend on this unit. Unit can only be deprovisioned if this list is empty. Maximum 1000.", "items": {"$ref": "UnitDependency"}, "readOnly": true, "type": "array"}, "etag": {"description": "Output only. An opaque value that uniquely identifies a version or generation of a resource. It can be used to confirm that the client and server agree on the ordering of a resource being written.", "readOnly": true, "type": "string"}, "inputVariables": {"description": "Optional. Output only. Indicates the current input variables deployed by the unit", "items": {"$ref": "UnitVariable"}, "readOnly": true, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels on the resource, which can be used for categorization. similar to Kubernetes resource labels.", "type": "object"}, "maintenance": {"$ref": "MaintenanceSettings", "description": "Optional. Captures requested directives for performing future maintenance on the unit. This includes a request for the unit to skip maintenance for a period of time and remain pinned to its current release as well as controls for postponing maintenance scheduled in future."}, "managementMode": {"description": "Optional. Immutable. Indicates whether the Unit life cycle is controlled by the user or by the system. Immutable once created.", "enum": ["MANAGEMENT_MODE_UNSPECIFIED", "MANAGEMENT_MODE_USER", "MANAGEMENT_MODE_SYSTEM"], "enumDescriptions": ["", "Unit's lifecycle is managed by the user.", "The system will decide when to deprovision and delete the unit. User still can deprovision or delete the unit manually."], "type": "string"}, "name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/units/{unit}\"", "type": "string"}, "ongoingOperations": {"description": "Optional. Output only. List of concurrent UnitOperations that are operating on this Unit.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "outputVariables": {"description": "Optional. Output only. Set of key/value pairs corresponding to output variables from execution of actuation templates. The variables are declared in actuation configs (e.g in helm chart or terraform) and the values are fetched and returned by the actuation engine upon completion of execution.", "items": {"$ref": "UnitVariable"}, "readOnly": true, "type": "array"}, "pendingOperations": {"description": "Optional. Output only. List of pending (wait to be executed) UnitOperations for this unit.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "release": {"description": "Optional. Output only. The current Release object for this Unit.", "readOnly": true, "type": "string"}, "scheduledOperations": {"description": "Optional. Output only. List of scheduled UnitOperations for this unit.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "state": {"description": "Optional. Output only. Current lifecycle state of the resource (e.g. if it's being created or ready to use).", "enum": ["UNIT_STATE_UNSPECIFIED", "UNIT_STATE_NOT_PROVISIONED", "UNIT_STATE_PROVISIONING", "UNIT_STATE_UPDATING", "UNIT_STATE_DEPROVISIONING", "UNIT_STATE_READY", "UNIT_STATE_ERROR"], "enumDescriptions": ["Unspecified state.", "Unit is not provisioned.", "Unit is being provisioned.", "Unit is being updated. This is typically when a unit is being upgraded to a new release or some of the input variables on the Unit is being changed. Certain kinds of updates may cause the Unit to become unusable while the update is in progress.", "Unit is being deleted.", "Unit has been provisioned and is ready for use", "Unit has error, when it is not ready and some error operation"], "readOnly": true, "type": "string"}, "systemCleanupAt": {"description": "Optional. Output only. If set, indicates the time when the system will start removing the unit.", "format": "google-datetime", "readOnly": true, "type": "string"}, "systemManagedState": {"description": "Optional. Output only. Indicates the system managed state of the unit.", "enum": ["SYSTEM_MANAGED_STATE_UNSPECIFIED", "SYSTEM_MANAGED_STATE_ACTIVE", "SYSTEM_MANAGED_STATE_INACTIVE", "SYSTEM_MANAGED_STATE_DECOMMISSIONED"], "enumDescriptions": ["", "Unit has dependents attached.", "Unit has no dependencies attached, but attachment is allowed.", "Unit has no dependencies attached, and attachment is not allowed."], "readOnly": true, "type": "string"}, "tenant": {"description": "Optional. Reference to the Saas Tenant resource this unit belongs to. This for example informs the maintenance policies to use for scheduling future updates on a unit. (optional and immutable once created)", "type": "string"}, "uid": {"description": "Output only. The unique identifier of the resource. UID is unique in the time and space for this resource within the scope of the service. It is typically generated by the server on successful creation of a resource and must not be changed. UID is used to uniquely identify resources with resource name reuses. This should be a UUID4.", "readOnly": true, "type": "string"}, "unitKind": {"description": "Optional. Reference to the UnitKind this Unit belongs to. Immutable once set.", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was last updated. Any change to the resource made by users must refresh this value. Changes to a resource made by the service should refresh this value.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "UnitCondition": {"description": "UnitCondition describes the status of an Unit. UnitCondition is individual components that contribute to an overall state.", "id": "UnitCondition", "properties": {"lastTransitionTime": {"description": "Required. Last time the condition transited from one status to another.", "format": "google-datetime", "type": "string"}, "message": {"description": "Required. Human readable message indicating details about the last transition.", "type": "string"}, "reason": {"description": "Required. Brief reason for the condition's last transition.", "type": "string"}, "status": {"description": "Required. Status of the condition.", "enum": ["STATUS_UNSPECIFIED", "STATUS_UNKNOWN", "STATUS_TRUE", "STATUS_FALSE"], "enumDescriptions": ["Condition status is unspecified.", "Condition is unknown.", "Condition is true.", "Condition is false."], "type": "string"}, "type": {"description": "Required. Type of the condition.", "enum": ["TYPE_UNSPECIFIED", "TYPE_READY", "TYPE_UPDATING", "TYPE_PROVISIONED", "TYPE_OPERATION_ERROR"], "enumDescriptions": ["Condition type is unspecified.", "Condition type is ready.", "Condition type is updating.", "Condition type is provisioned.", "Condition type is operationError. True when the last unit operation fails with a non-ignorable error."], "type": "string"}}, "type": "object"}, "UnitDependency": {"description": "Set of dependencies for this unit. Maximum 10.", "id": "UnitDependency", "properties": {"alias": {"description": "Output only. <PERSON><PERSON> for the name of the dependency.", "readOnly": true, "type": "string"}, "unit": {"description": "Output only. A reference to the Unit object.", "readOnly": true, "type": "string"}}, "type": "object"}, "UnitKind": {"description": "Definition of a Unit. Units belonging to the same UnitKind are managed together; for example they follow the same release model (blueprints, versions etc.) and are typically rolled out together.", "id": "UnitKind", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Annotations is an unstructured key-value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/user-guide/annotations", "type": "object"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "defaultRelease": {"description": "Optional. A reference to the Release object to use as default for creating new units of this UnitKind (optional). If not specified, a new unit must explicitly reference which release to use for its creation.", "type": "string"}, "dependencies": {"description": "Optional. Immutable. List of other unit kinds that this release will depend on. Dependencies will be automatically provisioned if not found. Maximum 10.", "items": {"$ref": "Dependency"}, "type": "array"}, "etag": {"description": "Output only. An opaque value that uniquely identifies a version or generation of a resource. It can be used to confirm that the client and server agree on the ordering of a resource being written.", "readOnly": true, "type": "string"}, "inputVariableMappings": {"description": "Optional. List of inputVariables for this release that will either be retrieved from a dependency’s outputVariables, or will be passed on to a dependency’s inputVariables. Maximum 100.", "items": {"$ref": "VariableMapping"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels on the resource, which can be used for categorization. similar to Kubernetes resource labels.", "type": "object"}, "name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/unitKinds/{unitKind}\"", "type": "string"}, "outputVariableMappings": {"description": "Optional. List of outputVariables for this unit kind will be passed to this unit's outputVariables. Maximum 100.", "items": {"$ref": "VariableMapping"}, "type": "array"}, "saas": {"description": "Required. Immutable. A reference to the Saas that defines the product (managed service) that the producer wants to manage with EasySaaS. Part of the EasySaaS common data model. Immutable once set.", "type": "string"}, "uid": {"description": "Output only. The unique identifier of the resource. UID is unique in the time and space for this resource within the scope of the service. It is typically generated by the server on successful creation of a resource and must not be changed. UID is used to uniquely identify resources with resource name reuses. This should be a UUID4.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was last updated. Any change to the resource made by users must refresh this value. Changes to a resource made by the service should refresh this value.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "UnitOperation": {"description": "UnitOperation encapsulates the intent of changing/interacting with the service component represented by the specific Unit. Multiple UnitOperations can be created (requested) and scheduled in the future, however only one will be allowed to execute at a time (that can change in the future for non-mutating operations). UnitOperations allow different actors interacting with the same unit to focus only on the change they have requested. This is a base object that contains the common fields in all unit operations.", "id": "UnitOperation", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Annotations is an unstructured key-value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/user-guide/annotations", "type": "object"}, "cancel": {"description": "Optional. When true, attempt to cancel the operation. Cancellation may fail if the operation is already executing. (Optional)", "type": "boolean"}, "conditions": {"description": "Optional. Output only. A set of conditions which indicate the various conditions this resource can have.", "items": {"$ref": "UnitOperationCondition"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deprovision": {"$ref": "Deprovision"}, "engineState": {"description": "Optional. Output only. The engine state for on-going deployment engine operation(s). This field is opaque for external usage.", "readOnly": true, "type": "string"}, "errorCategory": {"description": "Optional. Output only. UnitOperationErrorCategory describe the error category.", "enum": ["UNIT_OPERATION_ERROR_CATEGORY_UNSPECIFIED", "NOT_APPLICABLE", "FATAL", "RETRIABLE", "IGNORABLE", "STANDARD"], "enumDescriptions": ["Unit operation error category is unspecified", "Unit operation error category is not applicable, or it is not an error", "Unit operation error category is fatal", "Unit operation error category is retriable", "Unit operation error category is ignorable", "Unit operation error category is standard, counts towards Rollout error budget"], "readOnly": true, "type": "string"}, "etag": {"description": "Output only. An opaque value that uniquely identifies a version or generation of a resource. It can be used to confirm that the client and server agree on the ordering of a resource being written.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels on the resource, which can be used for categorization. similar to Kubernetes resource labels.", "type": "object"}, "name": {"description": "Identifier. The resource name (full URI of the resource) following the standard naming scheme: \"projects/{project}/locations/{location}/unitOperations/{unitOperation}\"", "type": "string"}, "parentUnitOperation": {"description": "Optional. Reference to parent resource: UnitOperation. If an operation needs to create other operations as part of its workflow, each of the child operations should have this field set to the parent. This can be used for tracing. (Optional)", "type": "string"}, "provision": {"$ref": "Provision"}, "rollout": {"description": "Optional. Specifies which rollout created this Unit Operation. This cannot be modified and is used for filtering purposes only. If a dependent unit and unit operation are created as part of another unit operation, they will use the same rolloutId.", "type": "string"}, "schedule": {"$ref": "Schedule", "description": "Optional. When to schedule this operation."}, "state": {"description": "Optional. Output only. UnitOperationState describes the current state of the unit operation.", "enum": ["UNIT_OPERATION_STATE_UNKNOWN", "UNIT_OPERATION_STATE_PENDING", "UNIT_OPERATION_STATE_SCHEDULED", "UNIT_OPERATION_STATE_RUNNING", "UNIT_OPERATION_STATE_SUCCEEDED", "UNIT_OPERATION_STATE_FAILED", "UNIT_OPERATION_STATE_CANCELLED"], "enumDescriptions": ["", "Unit operation is accepted but not ready to run.", "Unit operation is accepted and scheduled.", "Unit operation is running.", "Unit operation has completed successfully.", "Unit operation has failed.", "Unit operation was cancelled."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. The unique identifier of the resource. UID is unique in the time and space for this resource within the scope of the service. It is typically generated by the server on successful creation of a resource and must not be changed. UID is used to uniquely identify resources with resource name reuses. This should be a UUID4.", "readOnly": true, "type": "string"}, "unit": {"description": "Required. Immutable. The Unit a given UnitOperation will act upon.", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was last updated. Any change to the resource made by users must refresh this value. Changes to a resource made by the service should refresh this value.", "format": "google-datetime", "readOnly": true, "type": "string"}, "upgrade": {"$ref": "Upgrade"}}, "type": "object"}, "UnitOperationCondition": {"description": "UnitOperationCondition describes the status of an Unit Operation. UnitOperationCondition is individual components that contribute to an overall state.", "id": "UnitOperationCondition", "properties": {"lastTransitionTime": {"description": "Required. Last time the condition transited from one status to another.", "format": "google-datetime", "type": "string"}, "message": {"description": "Required. Human readable message indicating details about the last transition.", "type": "string"}, "reason": {"description": "Required. Brief reason for the condition's last transition.", "type": "string"}, "status": {"description": "Required. Status of the condition.", "enum": ["STATUS_UNSPECIFIED", "STATUS_UNKNOWN", "STATUS_TRUE", "STATUS_FALSE"], "enumDescriptions": ["Condition status is unspecified.", "Condition is unknown.", "Condition is true.", "Condition is false."], "type": "string"}, "type": {"description": "Required. Type of the condition.", "enum": ["TYPE_UNSPECIFIED", "TYPE_SCHEDULED", "TYPE_RUNNING", "TYPE_SUCCEEDED", "TYPE_CANCELLED"], "enumDescriptions": ["Condition type is unspecified.", "Condition type is scheduled.", "Condition type is running.", "Condition type is succeeded.", "Condition type is cancelled."], "type": "string"}}, "type": "object"}, "UnitVariable": {"description": "UnitVariable describes a parameter for a Unit.", "id": "UnitVariable", "properties": {"type": {"description": "Optional. Immutable. Name of a supported variable type. Supported types are string, int, bool.", "enum": ["TYPE_UNSPECIFIED", "STRING", "INT", "BOOL"], "enumDescriptions": ["Variable type is unspecified.", "Variable type is string.", "Variable type is int.", "Variable type is bool."], "type": "string"}, "value": {"description": "Optional. String encoded value for the variable.", "type": "string"}, "variable": {"description": "Required. Immutable. Name of the variable from actuation configs.", "type": "string"}}, "type": "object"}, "Upgrade": {"description": "Upgrade is the unit operation that upgrades a provisioned unit, which may also include the underlying resources represented by a Unit. Can only execute if the Unit is currently provisioned.", "id": "Upgrade", "properties": {"inputVariables": {"description": "Optional. Set of input variables. Maximum 100. (optional)", "items": {"$ref": "UnitVariable"}, "type": "array"}, "release": {"description": "Optional. Reference to the Release object to use for the Unit. (optional).", "type": "string"}}, "type": "object"}, "VariableMapping": {"description": "Mapping of input variables to their respective output variable for depedenencies", "id": "VariableMapping", "properties": {"from": {"$ref": "FromMapping", "description": "Optional. Output variables which will get their values from dependencies"}, "to": {"$ref": "ToMapping", "description": "Optional. Input variables whose values will be passed on to dependencies."}, "variable": {"description": "Required. name of the variable", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "SaaS Runtime API", "version": "v1beta1", "version_module": true}