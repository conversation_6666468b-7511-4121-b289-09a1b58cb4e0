{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://metastore.googleapis.com/", "batchPath": "batch", "canonicalName": "Dataproc Metastore", "description": "The Dataproc Metastore API is used to manage the lifecycle and configuration of metastore services.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/dataproc-metastore/docs", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "metastore:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://metastore.mtls.googleapis.com/", "name": "metastore", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "metastore.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "metastore.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like \"displayName=tokyo\", and is documented in more detail in AIP-160 (https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the next_page_token field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"federations": {"methods": {"create": {"description": "Creates a metastore federation in a project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/federations", "httpMethod": "POST", "id": "metastore.projects.locations.federations.create", "parameterOrder": ["parent"], "parameters": {"federationId": {"description": "Required. The ID of the metastore federation, which is used as the final component of the metastore federation's name.This value must be between 2 and 63 characters long inclusive, begin with a letter, end with a letter or number, and consist of alpha-numeric ASCII characters or hyphens.", "location": "query", "type": "string"}, "parent": {"description": "Required. The relative resource name of the location in which to create a federation service, in the following form:projects/{project_number}/locations/{location_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID. Specify a unique request ID to allow the server to ignore the request if it has completed. The server will ignore subsequent requests that provide a duplicate request ID for at least 60 minutes after the first request.For example, if an initial request times out, followed by another request with the same request ID, the server ignores the second request to prevent the creation of duplicate commitments.The request ID must be a valid UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/federations", "request": {"$ref": "Federation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single federation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/federations/{federationsId}", "httpMethod": "DELETE", "id": "metastore.projects.locations.federations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The relative resource name of the metastore federation to delete, in the following form:projects/{project_number}/locations/{location_id}/federations/{federation_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/federations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID. Specify a unique request ID to allow the server to ignore the request if it has completed. The server will ignore subsequent requests that provide a duplicate request ID for at least 60 minutes after the first request.For example, if an initial request times out, followed by another request with the same request ID, the server ignores the second request to prevent the creation of duplicate commitments.The request ID must be a valid UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a single federation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/federations/{federationsId}", "httpMethod": "GET", "id": "metastore.projects.locations.federations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The relative resource name of the metastore federation to retrieve, in the following form:projects/{project_number}/locations/{location_id}/federations/{federation_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/federations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Federation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/federations/{federationsId}:getIamPolicy", "httpMethod": "GET", "id": "metastore.projects.locations.federations.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy.Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected.Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset.The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1.To learn which resources support conditions in their IAM policies, see the IAM documentation (https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/federations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists federations in a project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/federations", "httpMethod": "GET", "id": "metastore.projects.locations.federations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. The filter to apply to list results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Specify the ordering of results as described in Sorting Order (https://cloud.google.com/apis/design/design_patterns#sorting_order). If not specified, the results will be sorted in the default order.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of federations to return. The response may contain less than the maximum number. If unspecified, no more than 500 services are returned. The maximum value is 1000; values above 1000 are changed to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous ListFederationServices call. Provide this token to retrieve the subsequent page.To retrieve the first page, supply an empty page token.When paginating, other parameters provided to ListFederationServices must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The relative resource name of the location of metastore federations to list, in the following form: projects/{project_number}/locations/{location_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/federations", "response": {"$ref": "ListFederationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the fields of a federation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/federations/{federationsId}", "httpMethod": "PATCH", "id": "metastore.projects.locations.federations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The relative resource name of the federation, of the form: projects/{project_number}/locations/{location_id}/federations/{federation_id}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/federations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID. Specify a unique request ID to allow the server to ignore the request if it has completed. The server will ignore subsequent requests that provide a duplicate request ID for at least 60 minutes after the first request.For example, if an initial request times out, followed by another request with the same request ID, the server ignores the second request to prevent the creation of duplicate commitments.The request ID must be a valid UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.", "location": "query", "type": "string"}, "updateMask": {"description": "Required. A field mask used to specify the fields to be overwritten in the metastore federation resource by the update. Fields specified in the update_mask are relative to the resource (not to the full request). A field is overwritten if it is in the mask.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Federation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/federations/{federationsId}:setIamPolicy", "httpMethod": "POST", "id": "metastore.projects.locations.federations.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/federations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/federations/{federationsId}:testIamPermissions", "httpMethod": "POST", "id": "metastore.projects.locations.federations.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/federations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to Code.CANCELLED.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "metastore.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "metastore.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "metastore.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "metastore.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "services": {"methods": {"alterLocation": {"description": "Alter metadata resource location. The metadata resource can be a database, table, or partition. This functionality only updates the parent directory for the respective metadata resource and does not transfer any existing data to the new location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:alterLocation", "httpMethod": "POST", "id": "metastore.projects.locations.services.alterLocation", "parameterOrder": ["service"], "parameters": {"service": {"description": "Required. The relative resource name of the metastore service to mutate metadata, in the following format:projects/{project_id}/locations/{location_id}/services/{service_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+service}:alterLocation", "request": {"$ref": "AlterMetadataResourceLocationRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "alterTableProperties": {"description": "Alter metadata table properties.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:alterTableProperties", "httpMethod": "POST", "id": "metastore.projects.locations.services.alterTableProperties", "parameterOrder": ["service"], "parameters": {"service": {"description": "Required. The relative resource name of the Dataproc Metastore service that's being used to mutate metadata table properties, in the following format:projects/{project_id}/locations/{location_id}/services/{service_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+service}:alterTableProperties", "request": {"$ref": "AlterTablePropertiesRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "cancelMigration": {"description": "Cancels the ongoing Managed Migration process.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:cancelMigration", "httpMethod": "POST", "id": "metastore.projects.locations.services.cancelMigration", "parameterOrder": ["service"], "parameters": {"service": {"description": "Required. The relative resource name of the metastore service to cancel the ongoing migration to, in the following format:projects/{project_id}/locations/{location_id}/services/{service_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+service}:cancelMigration", "request": {"$ref": "CancelMigrationRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "completeMigration": {"description": "Completes the managed migration process. The Dataproc Metastore service will switch to using its own backend database after successful migration.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:completeMigration", "httpMethod": "POST", "id": "metastore.projects.locations.services.completeMigration", "parameterOrder": ["service"], "parameters": {"service": {"description": "Required. The relative resource name of the metastore service to complete the migration to, in the following format:projects/{project_id}/locations/{location_id}/services/{service_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+service}:completeMigration", "request": {"$ref": "CompleteMigrationRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a metastore service in a project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services", "httpMethod": "POST", "id": "metastore.projects.locations.services.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The relative resource name of the location in which to create a metastore service, in the following form:projects/{project_number}/locations/{location_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID. Specify a unique request ID to allow the server to ignore the request if it has completed. The server will ignore subsequent requests that provide a duplicate request ID for at least 60 minutes after the first request.For example, if an initial request times out, followed by another request with the same request ID, the server ignores the second request to prevent the creation of duplicate commitments.The request ID must be a valid UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.", "location": "query", "type": "string"}, "serviceId": {"description": "Required. The ID of the metastore service, which is used as the final component of the metastore service's name.This value must be between 2 and 63 characters long inclusive, begin with a letter, end with a letter or number, and consist of alpha-numeric ASCII characters or hyphens.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/services", "request": {"$ref": "Service"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}", "httpMethod": "DELETE", "id": "metastore.projects.locations.services.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The relative resource name of the metastore service to delete, in the following form:projects/{project_number}/locations/{location_id}/services/{service_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID. Specify a unique request ID to allow the server to ignore the request if it has completed. The server will ignore subsequent requests that provide a duplicate request ID for at least 60 minutes after the first request.For example, if an initial request times out, followed by another request with the same request ID, the server ignores the second request to prevent the creation of duplicate commitments.The request ID must be a valid UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "exportMetadata": {"description": "Exports metadata from a service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:exportMetadata", "httpMethod": "POST", "id": "metastore.projects.locations.services.exportMetadata", "parameterOrder": ["service"], "parameters": {"service": {"description": "Required. The relative resource name of the metastore service to run export, in the following form:projects/{project_id}/locations/{location_id}/services/{service_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+service}:exportMetadata", "request": {"$ref": "ExportMetadataRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a single service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}", "httpMethod": "GET", "id": "metastore.projects.locations.services.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The relative resource name of the metastore service to retrieve, in the following form:projects/{project_number}/locations/{location_id}/services/{service_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Service"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:getIamPolicy", "httpMethod": "GET", "id": "metastore.projects.locations.services.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy.Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected.Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset.The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1.To learn which resources support conditions in their IAM policies, see the IAM documentation (https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists services in a project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services", "httpMethod": "GET", "id": "metastore.projects.locations.services.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. The filter to apply to list results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Specify the ordering of results as described in Sorting Order (https://cloud.google.com/apis/design/design_patterns#sorting_order). If not specified, the results will be sorted in the default order.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of services to return. The response may contain less than the maximum number. If unspecified, no more than 500 services are returned. The maximum value is 1000; values above 1000 are changed to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous DataprocMetastore.ListServices call. Provide this token to retrieve the subsequent page.To retrieve the first page, supply an empty page token.When paginating, other parameters provided to DataprocMetastore.ListServices must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The relative resource name of the location of metastore services to list, in the following form:projects/{project_number}/locations/{location_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/services", "response": {"$ref": "ListServicesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "moveTableToDatabase": {"description": "Move a table to another database.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:moveTableToDatabase", "httpMethod": "POST", "id": "metastore.projects.locations.services.moveTableToDatabase", "parameterOrder": ["service"], "parameters": {"service": {"description": "Required. The relative resource name of the metastore service to mutate metadata, in the following format:projects/{project_id}/locations/{location_id}/services/{service_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+service}:moveTableToDatabase", "request": {"$ref": "MoveTableToDatabaseRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}", "httpMethod": "PATCH", "id": "metastore.projects.locations.services.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. The relative resource name of the metastore service, in the following format:projects/{project_number}/locations/{location_id}/services/{service_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID. Specify a unique request ID to allow the server to ignore the request if it has completed. The server will ignore subsequent requests that provide a duplicate request ID for at least 60 minutes after the first request.For example, if an initial request times out, followed by another request with the same request ID, the server ignores the second request to prevent the creation of duplicate commitments.The request ID must be a valid UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.", "location": "query", "type": "string"}, "updateMask": {"description": "Required. A field mask used to specify the fields to be overwritten in the metastore service resource by the update. Fields specified in the update_mask are relative to the resource (not to the full request). A field is overwritten if it is in the mask.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Service"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "queryMetadata": {"description": "Query Dataproc Metastore metadata.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:queryMetadata", "httpMethod": "POST", "id": "metastore.projects.locations.services.queryMetadata", "parameterOrder": ["service"], "parameters": {"service": {"description": "Required. The relative resource name of the metastore service to query metadata, in the following format:projects/{project_id}/locations/{location_id}/services/{service_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+service}:queryMetadata", "request": {"$ref": "QueryMetadataRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "restore": {"description": "Restores a service from a backup.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:restore", "httpMethod": "POST", "id": "metastore.projects.locations.services.restore", "parameterOrder": ["service"], "parameters": {"service": {"description": "Required. The relative resource name of the metastore service to run restore, in the following form:projects/{project_id}/locations/{location_id}/services/{service_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+service}:restore", "request": {"$ref": "RestoreServiceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:setIamPolicy", "httpMethod": "POST", "id": "metastore.projects.locations.services.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "startMigration": {"description": "Starts the Managed Migration process.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:startMigration", "httpMethod": "POST", "id": "metastore.projects.locations.services.startMigration", "parameterOrder": ["service"], "parameters": {"service": {"description": "Required. The relative resource name of the metastore service to start migrating to, in the following format:projects/{project_id}/locations/{location_id}/services/{service_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+service}:startMigration", "request": {"$ref": "StartMigrationRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:testIamPermissions", "httpMethod": "POST", "id": "metastore.projects.locations.services.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"backups": {"methods": {"create": {"description": "Creates a new backup in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/backups", "httpMethod": "POST", "id": "metastore.projects.locations.services.backups.create", "parameterOrder": ["parent"], "parameters": {"backupId": {"description": "Required. The ID of the backup, which is used as the final component of the backup's name.This value must be between 1 and 64 characters long, begin with a letter, end with a letter or number, and consist of alpha-numeric ASCII characters or hyphens.", "location": "query", "type": "string"}, "parent": {"description": "Required. The relative resource name of the service in which to create a backup of the following form:projects/{project_number}/locations/{location_id}/services/{service_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID. Specify a unique request ID to allow the server to ignore the request if it has completed. The server will ignore subsequent requests that provide a duplicate request ID for at least 60 minutes after the first request.For example, if an initial request times out, followed by another request with the same request ID, the server ignores the second request to prevent the creation of duplicate commitments.The request ID must be a valid UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/backups", "request": {"$ref": "Backup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single backup.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/backups/{backupsId}", "httpMethod": "DELETE", "id": "metastore.projects.locations.services.backups.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The relative resource name of the backup to delete, in the following form:projects/{project_number}/locations/{location_id}/services/{service_id}/backups/{backup_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/backups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID. Specify a unique request ID to allow the server to ignore the request if it has completed. The server will ignore subsequent requests that provide a duplicate request ID for at least 60 minutes after the first request.For example, if an initial request times out, followed by another request with the same request ID, the server ignores the second request to prevent the creation of duplicate commitments.The request ID must be a valid UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single backup.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/backups/{backupsId}", "httpMethod": "GET", "id": "metastore.projects.locations.services.backups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The relative resource name of the backup to retrieve, in the following form:projects/{project_number}/locations/{location_id}/services/{service_id}/backups/{backup_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/backups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Backup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/backups/{backupsId}:getIamPolicy", "httpMethod": "GET", "id": "metastore.projects.locations.services.backups.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy.Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected.Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset.The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1.To learn which resources support conditions in their IAM policies, see the IAM documentation (https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/backups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists backups in a service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/backups", "httpMethod": "GET", "id": "metastore.projects.locations.services.backups.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. The filter to apply to list results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Specify the ordering of results as described in Sorting Order (https://cloud.google.com/apis/design/design_patterns#sorting_order). If not specified, the results will be sorted in the default order.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of backups to return. The response may contain less than the maximum number. If unspecified, no more than 500 backups are returned. The maximum value is 1000; values above 1000 are changed to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous DataprocMetastore.ListBackups call. Provide this token to retrieve the subsequent page.To retrieve the first page, supply an empty page token.When paginating, other parameters provided to DataprocMetastore.ListBackups must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The relative resource name of the service whose backups to list, in the following form:projects/{project_number}/locations/{location_id}/services/{service_id}/backups.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/backups", "response": {"$ref": "ListBackupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/backups/{backupsId}:setIamPolicy", "httpMethod": "POST", "id": "metastore.projects.locations.services.backups.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/backups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "databases": {"methods": {"getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/databases/{databasesId}:getIamPolicy", "httpMethod": "GET", "id": "metastore.projects.locations.services.databases.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy.Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected.Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset.The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1.To learn which resources support conditions in their IAM policies, see the IAM documentation (https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/databases/{databasesId}:setIamPolicy", "httpMethod": "POST", "id": "metastore.projects.locations.services.databases.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"tables": {"methods": {"getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/databases/{databasesId}/tables/{tablesId}:getIamPolicy", "httpMethod": "GET", "id": "metastore.projects.locations.services.databases.tables.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy.Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected.Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset.The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1.To learn which resources support conditions in their IAM policies, see the IAM documentation (https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/databases/[^/]+/tables/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/databases/{databasesId}/tables/{tablesId}:setIamPolicy", "httpMethod": "POST", "id": "metastore.projects.locations.services.databases.tables.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/databases/[^/]+/tables/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "metadataImports": {"methods": {"create": {"description": "Creates a new MetadataImport in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/metadataImports", "httpMethod": "POST", "id": "metastore.projects.locations.services.metadataImports.create", "parameterOrder": ["parent"], "parameters": {"metadataImportId": {"description": "Required. The ID of the metadata import, which is used as the final component of the metadata import's name.This value must be between 1 and 64 characters long, begin with a letter, end with a letter or number, and consist of alpha-numeric ASCII characters or hyphens.", "location": "query", "type": "string"}, "parent": {"description": "Required. The relative resource name of the service in which to create a metastore import, in the following form:projects/{project_number}/locations/{location_id}/services/{service_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID. Specify a unique request ID to allow the server to ignore the request if it has completed. The server will ignore subsequent requests that provide a duplicate request ID for at least 60 minutes after the first request.For example, if an initial request times out, followed by another request with the same request ID, the server ignores the second request to prevent the creation of duplicate commitments.The request ID must be a valid UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/metadataImports", "request": {"$ref": "MetadataImport"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single import.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/metadataImports/{metadataImportsId}", "httpMethod": "GET", "id": "metastore.projects.locations.services.metadataImports.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The relative resource name of the metadata import to retrieve, in the following form:projects/{project_number}/locations/{location_id}/services/{service_id}/metadataImports/{import_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/metadataImports/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "MetadataImport"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists imports in a service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/metadataImports", "httpMethod": "GET", "id": "metastore.projects.locations.services.metadataImports.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. The filter to apply to list results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Specify the ordering of results as described in Sorting Order (https://cloud.google.com/apis/design/design_patterns#sorting_order). If not specified, the results will be sorted in the default order.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of imports to return. The response may contain less than the maximum number. If unspecified, no more than 500 imports are returned. The maximum value is 1000; values above 1000 are changed to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous DataprocMetastore.ListServices call. Provide this token to retrieve the subsequent page.To retrieve the first page, supply an empty page token.When paginating, other parameters provided to DataprocMetastore.ListServices must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The relative resource name of the service whose metadata imports to list, in the following form:projects/{project_number}/locations/{location_id}/services/{service_id}/metadataImports.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/metadataImports", "response": {"$ref": "ListMetadataImportsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a single import. Only the description field of MetadataImport is supported to be updated.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/metadataImports/{metadataImportsId}", "httpMethod": "PATCH", "id": "metastore.projects.locations.services.metadataImports.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. The relative resource name of the metadata import, of the form:projects/{project_number}/locations/{location_id}/services/{service_id}/metadataImports/{metadata_import_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/metadataImports/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID. Specify a unique request ID to allow the server to ignore the request if it has completed. The server will ignore subsequent requests that provide a duplicate request ID for at least 60 minutes after the first request.For example, if an initial request times out, followed by another request with the same request ID, the server ignores the second request to prevent the creation of duplicate commitments.The request ID must be a valid UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.", "location": "query", "type": "string"}, "updateMask": {"description": "Required. A field mask used to specify the fields to be overwritten in the metadata import resource by the update. Fields specified in the update_mask are relative to the resource (not to the full request). A field is overwritten if it is in the mask.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "MetadataImport"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "migrationExecutions": {"methods": {"delete": {"description": "Deletes a single migration execution.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/migrationExecutions/{migrationExecutionsId}", "httpMethod": "DELETE", "id": "metastore.projects.locations.services.migrationExecutions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The relative resource name of the migrationExecution to delete, in the following form:projects/{project_number}/locations/{location_id}/services/{service_id}/migrationExecutions/{migration_execution_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/migrationExecutions/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID. Specify a unique request ID to allow the server to ignore the request if it has completed. The server will ignore subsequent requests that provide a duplicate request ID for at least 60 minutes after the first request.For example, if an initial request times out, followed by another request with the same request ID, the server ignores the second request to prevent the creation of duplicate commitments.The request ID must be a valid UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single migration execution.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/migrationExecutions/{migrationExecutionsId}", "httpMethod": "GET", "id": "metastore.projects.locations.services.migrationExecutions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The relative resource name of the migration execution to retrieve, in the following form:projects/{project_number}/locations/{location_id}/services/{service_id}/migrationExecutions/{migration_execution_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+/migrationExecutions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "MigrationExecution"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists migration executions on a service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/migrationExecutions", "httpMethod": "GET", "id": "metastore.projects.locations.services.migrationExecutions.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. The filter to apply to list results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Specify the ordering of results as described in Sorting Order (https://cloud.google.com/apis/design/design_patterns#sorting_order). If not specified, the results will be sorted in the default order.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of migration executions to return. The response may contain less than the maximum number. If unspecified, no more than 500 migration executions are returned. The maximum value is 1000; values above 1000 are changed to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous DataprocMetastore.ListMigrationExecutions call. Provide this token to retrieve the subsequent page.To retrieve the first page, supply an empty page token.When paginating, other parameters provided to DataprocMetastore.ListMigrationExecutions must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The relative resource name of the service whose migration executions to list, in the following form:projects/{project_number}/locations/{location_id}/services/{service_id}/migrationExecutions.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/migrationExecutions", "response": {"$ref": "ListMigrationExecutionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}, "revision": "20250514", "rootUrl": "https://metastore.googleapis.com/", "schemas": {"AlterMetadataResourceLocationRequest": {"description": "Request message for DataprocMetastore.AlterMetadataResourceLocation.", "id": "AlterMetadataResourceLocationRequest", "properties": {"locationUri": {"description": "Required. The new location URI for the metadata resource.", "type": "string"}, "resourceName": {"description": "Required. The relative metadata resource name in the following format.databases/{database_id} or databases/{database_id}/tables/{table_id} or databases/{database_id}/tables/{table_id}/partitions/{partition_id}", "type": "string"}}, "type": "object"}, "AlterMetadataResourceLocationResponse": {"description": "Response message for DataprocMetastore.AlterMetadataResourceLocation.", "id": "AlterMetadataResourceLocationResponse", "properties": {}, "type": "object"}, "AlterTablePropertiesRequest": {"description": "Request message for DataprocMetastore.AlterTableProperties.", "id": "AlterTablePropertiesRequest", "properties": {"properties": {"additionalProperties": {"type": "string"}, "description": "A map that describes the desired values to mutate. If update_mask is empty, the properties will not update. Otherwise, the properties only alters the value whose associated paths exist in the update mask", "type": "object"}, "tableName": {"description": "Required. The name of the table containing the properties you're altering in the following format.databases/{database_id}/tables/{table_id}", "type": "string"}, "updateMask": {"description": "A field mask that specifies the metadata table properties that are overwritten by the update. Fields specified in the update_mask are relative to the resource (not to the full request). A field is overwritten if it is in the mask.For example, given the target properties: properties { a: 1 b: 2 } And an update properties: properties { a: 2 b: 3 c: 4 } then if the field mask is:paths: \"properties.b\", \"properties.c\"then the result will be: properties { a: 1 b: 3 c: 4 } ", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs.If there are AuditConfigs for both allServices and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted.Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It <NAME_EMAIL> from DATA_READ logging, and <EMAIL> from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, storage.googleapis.com, cloudsql.googleapis.com. allServices is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "AutoscalingConfig": {"description": "Represents the autoscaling configuration of a metastore service.", "id": "AutoscalingConfig", "properties": {"autoscalingEnabled": {"description": "Optional. Whether or not autoscaling is enabled for this service.", "type": "boolean"}, "autoscalingFactor": {"description": "Output only. The scaling factor of a service with autoscaling enabled.", "format": "float", "readOnly": true, "type": "number"}, "limitConfig": {"$ref": "LimitConfig", "description": "Optional. The LimitConfig of the service."}}, "type": "object"}, "AuxiliaryVersionConfig": {"description": "Configuration information for the auxiliary service versions.", "id": "AuxiliaryVersionConfig", "properties": {"configOverrides": {"additionalProperties": {"type": "string"}, "description": "Optional. A mapping of Hive metastore configuration key-value pairs to apply to the auxiliary Hive metastore (configured in hive-site.xml) in addition to the primary version's overrides. If keys are present in both the auxiliary version's overrides and the primary version's overrides, the value from the auxiliary version's overrides takes precedence.", "type": "object"}, "networkConfig": {"$ref": "NetworkConfig", "description": "Output only. The network configuration contains the endpoint URI(s) of the auxiliary Hive metastore service.", "readOnly": true}, "version": {"description": "Optional. The Hive metastore version of the auxiliary service. It must be less than the primary Hive metastore service's version.", "type": "string"}}, "type": "object"}, "BackendMetastore": {"description": "Represents a backend metastore for the federation.", "id": "BackendMetastore", "properties": {"metastoreType": {"description": "The type of the backend metastore.", "enum": ["METASTORE_TYPE_UNSPECIFIED", "BIGQUERY", "DATAPROC_METASTORE"], "enumDescriptions": ["The metastore type is not set.", "The backend metastore is BigQuery.", "The backend metastore is Dataproc Metastore."], "type": "string"}, "name": {"description": "The relative resource name of the metastore that is being federated. The formats of the relative resource names for the currently supported metastores are listed below: BigQuery projects/{project_id} Dataproc Metastore projects/{project_id}/locations/{location}/services/{service_id}", "type": "string"}}, "type": "object"}, "Backup": {"description": "The details of a backup resource.", "id": "Backup", "properties": {"createTime": {"description": "Output only. The time when the backup was started.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. The description of the backup.", "type": "string"}, "endTime": {"description": "Output only. The time when the backup finished creating.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Immutable. Identifier. The relative resource name of the backup, in the following form:projects/{project_number}/locations/{location_id}/services/{service_id}/backups/{backup_id}", "type": "string"}, "restoringServices": {"description": "Output only. Services that are restoring from the backup.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "serviceRevision": {"$ref": "Service", "description": "Output only. The revision of the service at the time of backup.", "readOnly": true}, "state": {"description": "Output only. The current state of the backup.", "enum": ["STATE_UNSPECIFIED", "CREATING", "DELETING", "ACTIVE", "FAILED", "RESTORING"], "enumDescriptions": ["The state of the backup is unknown.", "The backup is being created.", "The backup is being deleted.", "The backup is active and ready to use.", "The backup failed.", "The backup is being restored."], "readOnly": true, "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates members, or principals, with a role.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding.If the condition evaluates to true, then this binding applies to the current request.If the condition evaluates to false, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding.To learn which resources support conditions in their IAM policies, see the IAM documentation (https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. members can have the following values: allUsers: A special identifier that represents anyone who is on the internet; with or without a Google account. allAuthenticatedUsers: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. user:{emailid}: An email address that represents a specific Google account. For example, <EMAIL> . serviceAccount:{emailid}: An email address that represents a Google service account. For example, <EMAIL>. serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]: An identifier for a Kubernetes service account (https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, my-project.svc.id.goog[my-namespace/my-kubernetes-sa]. group:{emailid}: An email address that represents a Google group. For example, <EMAIL>. domain:{domain}: The G Suite domain (primary) that represents all the users of that domain. For example, google.com or example.com. principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}: A single identity in a workforce identity pool. principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}: All workforce identities in a group. principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}: All workforce identities with a specific attribute value. principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*: All identities in a workforce identity pool. principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}: A single identity in a workload identity pool. principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}: A workload identity pool group. principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}: All identities in a workload identity pool with a certain attribute. principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*: All identities in a workload identity pool. deleted:user:{emailid}?uid={uniqueid}: An email address (plus unique identifier) representing a user that has been recently deleted. For example, <EMAIL>?uid=123456789012345678901. If the user is recovered, this value reverts to user:{emailid} and the recovered user retains the role in the binding. deleted:serviceAccount:{emailid}?uid={uniqueid}: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, <EMAIL>?uid=123456789012345678901. If the service account is undeleted, this value reverts to serviceAccount:{emailid} and the undeleted service account retains the role in the binding. deleted:group:{emailid}?uid={uniqueid}: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, <EMAIL>?uid=123456789012345678901. If the group is recovered, this value reverts to group:{emailid} and the recovered group retains the role in the binding. deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}: Deleted single identity in a workforce identity pool. For example, deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of members, or principals. For example, roles/viewer, roles/editor, or roles/owner.For an overview of the IAM roles and permissions, see the IAM documentation (https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see here (https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "CancelMigrationRequest": {"description": "Request message for DataprocMetastore.CancelMigration.", "id": "CancelMigrationRequest", "properties": {}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "CdcConfig": {"description": "Configuration information to start the Change Data Capture (CDC) streams from customer database to backend database of Dataproc Metastore.", "id": "CdcConfig", "properties": {"bucket": {"description": "Optional. The bucket to write the intermediate stream event data in. The bucket name must be without any prefix like \"gs://\". See the bucket naming requirements (https://cloud.google.com/storage/docs/buckets#naming). This field is optional. If not set, the Artifacts Cloud Storage bucket will be used.", "type": "string"}, "password": {"description": "Required. Input only. The password for the user that Datastream service should use for the MySQL connection. This field is not returned on request.", "type": "string"}, "reverseProxySubnet": {"description": "Required. The URL of the subnetwork resource to create the VM instance hosting the reverse proxy in. More context in https://cloud.google.com/datastream/docs/private-connectivity#reverse-csql-proxy The subnetwork should reside in the network provided in the request that Datastream will peer to and should be in the same region as Datastream, in the following format. projects/{project_id}/regions/{region_id}/subnetworks/{subnetwork_id}", "type": "string"}, "rootPath": {"description": "Optional. The root path inside the Cloud Storage bucket. The stream event data will be written to this path. The default value is /migration.", "type": "string"}, "subnetIpRange": {"description": "Required. A /29 CIDR IP range for peering with datastream.", "type": "string"}, "username": {"description": "Required. The username that the Datastream service should use for the MySQL connection.", "type": "string"}, "vpcNetwork": {"description": "Required. Fully qualified name of the Cloud SQL instance's VPC network or the shared VPC network that Datastream will peer to, in the following format: projects/{project_id}/locations/global/networks/{network_id}. More context in https://cloud.google.com/datastream/docs/network-connectivity-options#privateconnectivity", "type": "string"}}, "type": "object"}, "CloudSQLConnectionConfig": {"description": "Configuration information to establish customer database connection before the cutover phase of migration", "id": "CloudSQLConnectionConfig", "properties": {"hiveDatabaseName": {"description": "Required. The hive database name.", "type": "string"}, "instanceConnectionName": {"description": "Required. Cloud SQL database connection name (project_id:region:instance_name)", "type": "string"}, "ipAddress": {"description": "Required. The private IP address of the Cloud SQL instance.", "type": "string"}, "natSubnet": {"description": "Required. The relative resource name of the subnetwork to be used for Private Service Connect. Note that this cannot be a regular subnet and is used only for NAT. (https://cloud.google.com/vpc/docs/about-vpc-hosted-services#psc-subnets) This subnet is used to publish the SOCKS5 proxy service. The subnet size must be at least /29 and it should reside in a network through which the Cloud SQL instance is accessible. The resource name should be in the format, projects/{project_id}/regions/{region_id}/subnetworks/{subnetwork_id}", "type": "string"}, "password": {"description": "Required. Input only. The password for the user that Dataproc Metastore service will be using to connect to the database. This field is not returned on request.", "type": "string"}, "port": {"description": "Required. The network port of the database.", "format": "int32", "type": "integer"}, "proxySubnet": {"description": "Required. The relative resource name of the subnetwork to deploy the SOCKS5 proxy service in. The subnetwork should reside in a network through which the Cloud SQL instance is accessible. The resource name should be in the format, projects/{project_id}/regions/{region_id}/subnetworks/{subnetwork_id}", "type": "string"}, "username": {"description": "Required. The username that Dataproc Metastore service will use to connect to the database.", "type": "string"}}, "type": "object"}, "CloudSQLMigrationConfig": {"description": "Configuration information for migrating from self-managed hive metastore on Google Cloud using Cloud SQL as the backend database to Dataproc Metastore.", "id": "CloudSQLMigrationConfig", "properties": {"cdcConfig": {"$ref": "CdcConfig", "description": "Required. Configuration information to start the Change Data Capture (CDC) streams from customer database to backend database of Dataproc Metastore. Dataproc Metastore switches to using its backend database after the cutover phase of migration."}, "cloudSqlConnectionConfig": {"$ref": "CloudSQLConnectionConfig", "description": "Required. Configuration information to establish customer database connection before the cutover phase of migration"}}, "type": "object"}, "CompleteMigrationRequest": {"description": "Request message for DataprocMetastore.CompleteMigration.", "id": "CompleteMigrationRequest", "properties": {}, "type": "object"}, "Consumer": {"description": "Contains information of the customer's network configurations.", "id": "Consumer", "properties": {"endpointLocation": {"description": "Output only. The location of the endpoint URI. Format: projects/{project}/locations/{location}.", "readOnly": true, "type": "string"}, "endpointUri": {"description": "Output only. The URI of the endpoint used to access the metastore service.", "readOnly": true, "type": "string"}, "subnetwork": {"description": "Immutable. The subnetwork of the customer project from which an IP address is reserved and used as the Dataproc Metastore service's endpoint. It is accessible to hosts in the subnet and to all hosts in a subnet in the same region and same network. There must be at least one IP address available in the subnet's primary range. The subnet is specified in the following form:projects/{project_number}/regions/{region_id}/subnetworks/{subnetwork_id}", "type": "string"}}, "type": "object"}, "CustomRegionMetadata": {"description": "Metadata about a custom region. This is only populated if the region is a custom region. For single/multi regions, it will be empty.", "id": "CustomRegionMetadata", "properties": {"optionalReadOnlyRegions": {"description": "The read-only regions for this custom region.", "items": {"type": "string"}, "type": "array"}, "requiredReadWriteRegions": {"description": "The read-write regions for this custom region.", "items": {"type": "string"}, "type": "array"}, "witnessRegion": {"description": "The Spanner witness region for this custom region.", "type": "string"}}, "type": "object"}, "DataCatalogConfig": {"description": "Specifies how metastore metadata should be integrated with the Data Catalog service.", "id": "DataCatalogConfig", "properties": {"enabled": {"description": "Optional. Defines whether the metastore metadata should be synced to Data Catalog. The default value is to disable syncing metastore metadata to Data Catalog.", "type": "boolean"}}, "type": "object"}, "DatabaseDump": {"description": "A specification of the location of and metadata about a database dump from a relational database management system.", "id": "DatabaseDump", "properties": {"databaseType": {"deprecated": true, "description": "The type of the database.", "enum": ["DATABASE_TYPE_UNSPECIFIED", "MYSQL"], "enumDescriptions": ["The type of the source database is unknown.", "The type of the source database is MySQL."], "type": "string"}, "gcsUri": {"description": "Optional. A Cloud Storage object or folder URI that specifies the source from which to import metadata. It must begin with gs://.", "type": "string"}, "sourceDatabase": {"deprecated": true, "description": "Optional. The name of the source database.", "type": "string"}, "type": {"description": "Optional. The type of the database dump. If unspecified, defaults to MYSQL.", "enum": ["TYPE_UNSPECIFIED", "MYSQL", "AVRO"], "enumDescriptions": ["The type of the database dump is unknown.", "Database dump is a MySQL dump file.", "Database dump contains Avro files."], "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); } ", "id": "Empty", "properties": {}, "type": "object"}, "EncryptionConfig": {"description": "Encryption settings for the service.", "id": "EncryptionConfig", "properties": {"kmsKey": {"description": "Optional. The fully qualified customer provided Cloud KMS key name to use for customer data encryption, in the following format:projects/{project_number}/locations/{location_id}/keyRings/{key_ring_id}/cryptoKeys/{crypto_key_id}.", "type": "string"}}, "type": "object"}, "ErrorDetails": {"description": "Error details in public error message for DataprocMetastore.QueryMetadata.", "id": "ErrorDetails", "properties": {"details": {"additionalProperties": {"type": "string"}, "description": "Additional structured details about this error.Keys define the failure items. Value describes the exception or details of the item.", "type": "object"}}, "type": "object"}, "ExportMetadataRequest": {"description": "Request message for DataprocMetastore.ExportMetadata.", "id": "ExportMetadataRequest", "properties": {"databaseDumpType": {"description": "Optional. The type of the database dump. If unspecified, defaults to MYSQL.", "enum": ["TYPE_UNSPECIFIED", "MYSQL", "AVRO"], "enumDescriptions": ["The type of the database dump is unknown.", "Database dump is a MySQL dump file.", "Database dump contains Avro files."], "type": "string"}, "destinationGcsFolder": {"description": "A Cloud Storage URI of a folder, in the format gs:///. A sub-folder containing exported files will be created below it.", "type": "string"}, "requestId": {"description": "Optional. A request ID. Specify a unique request ID to allow the server to ignore the request if it has completed. The server will ignore subsequent requests that provide a duplicate request ID for at least 60 minutes after the first request.For example, if an initial request times out, followed by another request with the same request ID, the server ignores the second request to prevent the creation of duplicate commitments.The request ID must be a valid UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format). A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.", "type": "string"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec.Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "Federation": {"description": "Represents a federation of multiple backend metastores.", "id": "Federation", "properties": {"backendMetastores": {"additionalProperties": {"$ref": "BackendMetastore"}, "description": "A map from BackendMetastore rank to BackendMetastores from which the federation service serves metadata at query time. The map key represents the order in which BackendMetastores should be evaluated to resolve database names at query time and should be greater than or equal to zero. A BackendMetastore with a lower number will be evaluated before a BackendMetastore with a higher number.", "type": "object"}, "createTime": {"description": "Output only. The time when the metastore federation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endpointUri": {"description": "Output only. The federation endpoint.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "User-defined labels for the metastore federation.", "type": "object"}, "name": {"description": "Immutable. The relative resource name of the federation, of the form: projects/{project_number}/locations/{location_id}/federations/{federation_id}`.", "type": "string"}, "state": {"description": "Output only. The current state of the federation.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "UPDATING", "DELETING", "ERROR"], "enumDescriptions": ["The state of the metastore federation is unknown.", "The metastore federation is in the process of being created.", "The metastore federation is running and ready to serve queries.", "The metastore federation is being updated. It remains usable but cannot accept additional update requests or be deleted at this time.", "The metastore federation is undergoing deletion. It cannot be used.", "The metastore federation has encountered an error and cannot be used. The metastore federation should be deleted."], "readOnly": true, "type": "string"}, "stateMessage": {"description": "Output only. Additional information about the current state of the metastore federation, if available.", "readOnly": true, "type": "string"}, "uid": {"description": "Output only. The globally unique resource identifier of the metastore federation.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time when the metastore federation was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "version": {"description": "Immutable. The Apache Hive metastore version of the federation. All backend metastore versions must be compatible with the federation version.", "type": "string"}}, "type": "object"}, "HiveMetastoreConfig": {"description": "Specifies configuration information specific to running Hive metastore software as the metastore service.", "id": "HiveMetastoreConfig", "properties": {"auxiliaryVersions": {"additionalProperties": {"$ref": "AuxiliaryVersionConfig"}, "description": "Optional. A mapping of Hive metastore version to the auxiliary version configuration. When specified, a secondary Hive metastore service is created along with the primary service. All auxiliary versions must be less than the service's primary version. The key is the auxiliary service name and it must match the regular expression a-z?. This means that the first character must be a lowercase letter, and all the following characters must be hyphens, lowercase letters, or digits, except the last character, which cannot be a hyphen.", "type": "object"}, "configOverrides": {"additionalProperties": {"type": "string"}, "description": "Optional. A mapping of Hive metastore configuration key-value pairs to apply to the Hive metastore (configured in hive-site.xml). The mappings override system defaults (some keys cannot be overridden). These overrides are also applied to auxiliary versions and can be further customized in the auxiliary version's AuxiliaryVersionConfig.", "type": "object"}, "endpointProtocol": {"description": "Optional. The protocol to use for the metastore service endpoint. If unspecified, defaults to THRIFT.", "enum": ["ENDPOINT_PROTOCOL_UNSPECIFIED", "THRIFT", "GRPC"], "enumDescriptions": ["The protocol is not set.", "Use the legacy Apache Thrift protocol for the metastore service endpoint.", "Use the modernized gRPC protocol for the metastore service endpoint."], "type": "string"}, "kerberosConfig": {"$ref": "KerberosConfig", "description": "Optional. Information used to configure the Hive metastore service as a service principal in a Kerberos realm. To disable <PERSON>rber<PERSON>, use the UpdateService method and specify this field's path (hive_metastore_config.kerberos_config) in the request's update_mask while omitting this field from the request's service."}, "version": {"description": "Immutable. The Hive metastore schema version.", "type": "string"}}, "type": "object"}, "HiveMetastoreVersion": {"description": "A specification of a supported version of the Hive Metastore software.", "id": "HiveMetastoreVersion", "properties": {"isDefault": {"description": "Whether version will be chosen by the server if a metastore service is created with a HiveMetastoreConfig that omits the version.", "type": "boolean"}, "version": {"description": "The semantic version of the Hive Metastore software.", "type": "string"}}, "type": "object"}, "KerberosConfig": {"description": "Configuration information for a Kerberos principal.", "id": "KerberosConfig", "properties": {"keytab": {"$ref": "Secret", "description": "Optional. A Kerberos keytab file that can be used to authenticate a service principal with a Kerberos Key Distribution Center (KDC)."}, "krb5ConfigGcsUri": {"description": "Optional. A Cloud Storage URI that specifies the path to a krb5.conf file. It is of the form gs://{bucket_name}/path/to/krb5.conf, although the file does not need to be named krb5.conf explicitly.", "type": "string"}, "principal": {"description": "Optional. A Kerberos principal that exists in the both the keytab the KDC to authenticate as. A typical principal is of the form primary/instance@REALM, but there is no exact format.", "type": "string"}}, "type": "object"}, "LatestBackup": {"description": "The details of the latest scheduled backup.", "id": "LatestBackup", "properties": {"backupId": {"description": "Output only. The ID of an in-progress scheduled backup. Empty if no backup is in progress.", "readOnly": true, "type": "string"}, "duration": {"description": "Output only. The duration of the backup completion.", "format": "google-duration", "readOnly": true, "type": "string"}, "startTime": {"description": "Output only. The time when the backup was started.", "format": "google-datetime", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the backup.", "enum": ["STATE_UNSPECIFIED", "IN_PROGRESS", "SUCCEEDED", "FAILED"], "enumDescriptions": ["The state of the backup is unknown.", "The backup is in progress.", "The backup completed.", "The backup failed."], "readOnly": true, "type": "string"}}, "type": "object"}, "LimitConfig": {"description": "Represents the autoscaling limit configuration of a metastore service.", "id": "LimitConfig", "properties": {"maxScalingFactor": {"description": "Optional. The highest scaling factor that the service should be autoscaled to.", "format": "float", "type": "number"}, "minScalingFactor": {"description": "Optional. The lowest scaling factor that the service should be autoscaled to.", "format": "float", "type": "number"}}, "type": "object"}, "ListBackupsResponse": {"description": "Response message for DataprocMetastore.ListBackups.", "id": "ListBackupsResponse", "properties": {"backups": {"description": "The backups of the specified service.", "items": {"$ref": "Backup"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListFederationsResponse": {"description": "Response message for ListFederations", "id": "ListFederationsResponse", "properties": {"federations": {"description": "The services in the specified location.", "items": {"$ref": "Federation"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListMetadataImportsResponse": {"description": "Response message for DataprocMetastore.ListMetadataImports.", "id": "ListMetadataImportsResponse", "properties": {"metadataImports": {"description": "The imports in the specified service.", "items": {"$ref": "MetadataImport"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListMigrationExecutionsResponse": {"description": "Response message for DataprocMetastore.ListMigrationExecutions.", "id": "ListMigrationExecutionsResponse", "properties": {"migrationExecutions": {"description": "The migration executions on the specified service.", "items": {"$ref": "MigrationExecution"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListServicesResponse": {"description": "Response message for DataprocMetastore.ListServices.", "id": "ListServicesResponse", "properties": {"nextPageToken": {"description": "A token that can be sent as page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "services": {"description": "The services in the specified location.", "items": {"$ref": "Service"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"} ", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: \"us-east1\".", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: \"projects/example-project/locations/us-east1\"", "type": "string"}}, "type": "object"}, "LocationMetadata": {"description": "Metadata about the service in a location.", "id": "LocationMetadata", "properties": {"customRegionMetadata": {"description": "Possible configurations supported if the current region is a custom region.", "items": {"$ref": "CustomRegionMetadata"}, "type": "array"}, "multiRegionMetadata": {"$ref": "MultiRegionMetadata", "description": "The multi-region metadata if the current region is a multi-region."}, "supportedHiveMetastoreVersions": {"description": "The versions of Hive Metastore that can be used when creating a new metastore service in this location. The server guarantees that exactly one HiveMetastoreVersion in the list will set is_default.", "items": {"$ref": "HiveMetastoreVersion"}, "type": "array"}}, "type": "object"}, "MaintenanceWindow": {"description": "Maintenance window. This specifies when Dataproc Metastore may perform system maintenance operation to the service.", "id": "MaintenanceWindow", "properties": {"dayOfWeek": {"description": "Optional. The day of week, when the window starts.", "enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["The day of the week is unspecified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "string"}, "hourOfDay": {"description": "Optional. The hour of day (0-23) when the window starts.", "format": "int32", "type": "integer"}}, "type": "object"}, "MessageSet": {"deprecated": true, "description": "This is proto2's version of MessageSet.DEPRECATED: DO NOT USE FOR NEW FIELDS.If you are using editions or proto2, please make your own extendable messages for your use case. If you are using proto3, please use Any instead.MessageSet was the implementation of extensions for proto1. When proto2 was introduced, extensions were implemented as a first-class feature. This schema for MessageSet was meant to be a \"bridge\" solution to migrate MessageSet-bearing messages from proto1 to proto2.This schema has been open-sourced only to facilitate the migration of Google products with MessageSet-bearing messages to open-source environments.", "id": "MessageSet", "properties": {}, "type": "object"}, "MetadataExport": {"description": "The details of a metadata export operation.", "id": "MetadataExport", "properties": {"databaseDumpType": {"description": "Output only. The type of the database dump.", "enum": ["TYPE_UNSPECIFIED", "MYSQL", "AVRO"], "enumDescriptions": ["The type of the database dump is unknown.", "Database dump is a MySQL dump file.", "Database dump contains Avro files."], "readOnly": true, "type": "string"}, "destinationGcsUri": {"description": "Output only. A Cloud Storage URI of a folder that metadata are exported to, in the form of gs:////, where is automatically generated.", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time when the export ended.", "format": "google-datetime", "readOnly": true, "type": "string"}, "startTime": {"description": "Output only. The time when the export started.", "format": "google-datetime", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the export.", "enum": ["STATE_UNSPECIFIED", "RUNNING", "SUCCEEDED", "FAILED", "CANCELLED"], "enumDescriptions": ["The state of the metadata export is unknown.", "The metadata export is running.", "The metadata export completed successfully.", "The metadata export failed.", "The metadata export is cancelled."], "readOnly": true, "type": "string"}}, "type": "object"}, "MetadataImport": {"description": "A metastore resource that imports metadata.", "id": "MetadataImport", "properties": {"createTime": {"description": "Output only. The time when the metadata import was started.", "format": "google-datetime", "readOnly": true, "type": "string"}, "databaseDump": {"$ref": "DatabaseDump", "description": "Immutable. A database dump from a pre-existing metastore's database."}, "description": {"description": "Optional. The description of the metadata import.", "type": "string"}, "endTime": {"description": "Output only. The time when the metadata import finished.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Immutable. Identifier. The relative resource name of the metadata import, of the form:projects/{project_number}/locations/{location_id}/services/{service_id}/metadataImports/{metadata_import_id}.", "type": "string"}, "state": {"description": "Output only. The current state of the metadata import.", "enum": ["STATE_UNSPECIFIED", "RUNNING", "SUCCEEDED", "UPDATING", "FAILED"], "enumDescriptions": ["The state of the metadata import is unknown.", "The metadata import is running.", "The metadata import completed successfully.", "The metadata import is being updated.", "The metadata import failed, and attempted metadata changes were rolled back."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time when the metadata import was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "MetadataIntegration": {"description": "Specifies how metastore metadata should be integrated with external services.", "id": "MetadataIntegration", "properties": {"dataCatalogConfig": {"$ref": "DataCatalogConfig", "description": "Optional. The integration config for the Data Catalog service."}}, "type": "object"}, "MetadataManagementActivity": {"description": "The metadata management activities of the metastore service.", "id": "MetadataManagementActivity", "properties": {"metadataExports": {"description": "Output only. The latest metadata exports of the metastore service.", "items": {"$ref": "MetadataExport"}, "readOnly": true, "type": "array"}, "restores": {"description": "Output only. The latest restores of the metastore service.", "items": {"$ref": "Rest<PERSON>"}, "readOnly": true, "type": "array"}}, "type": "object"}, "MigrationExecution": {"description": "The details of a migration execution resource.", "id": "MigrationExecution", "properties": {"cloudSqlMigrationConfig": {"$ref": "CloudSQLMigrationConfig", "description": "Configuration information specific to migrating from self-managed hive metastore on Google Cloud using Cloud SQL as the backend database to Dataproc Metastore."}, "createTime": {"description": "Output only. The time when the migration execution was started.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time when the migration execution finished.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Output only. The relative resource name of the migration execution, in the following form: projects/{project_number}/locations/{location_id}/services/{service_id}/migrationExecutions/{migration_execution_id}", "readOnly": true, "type": "string"}, "phase": {"description": "Output only. The current phase of the migration execution.", "enum": ["PHASE_UNSPECIFIED", "REPLICATION", "CUTOVER"], "enumDescriptions": ["The phase of the migration execution is unknown.", "Replication phase refers to the migration phase when Dataproc Metastore is running a pipeline to replicate changes in the customer database to its backend database. During this phase, Dataproc Metastore uses the customer database as the hive metastore backend database.", "Cutover phase refers to the migration phase when Dataproc Metastore switches to using its own backend database. Migration enters this phase when customer is done migrating all their clusters/workloads to Dataproc Metastore and triggers CompleteMigration."], "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the migration execution.", "enum": ["STATE_UNSPECIFIED", "STARTING", "RUNNING", "CANCELLING", "AWAITING_USER_ACTION", "SUCCEEDED", "FAILED", "CANCELLED", "DELETING"], "enumDescriptions": ["The state of the migration execution is unknown.", "The migration execution is starting.", "The migration execution is running.", "The migration execution is in the process of being cancelled.", "The migration execution is awaiting user action.", "The migration execution has completed successfully.", "The migration execution has failed.", "The migration execution is cancelled.", "The migration execution is being deleted."], "readOnly": true, "type": "string"}, "stateMessage": {"description": "Output only. Additional information about the current state of the migration execution.", "readOnly": true, "type": "string"}}, "type": "object"}, "MoveTableToDatabaseRequest": {"description": "Request message for DataprocMetastore.MoveTableToDatabase.", "id": "MoveTableToDatabaseRequest", "properties": {"dbName": {"description": "Required. The name of the database where the table resides.", "type": "string"}, "destinationDbName": {"description": "Required. The name of the database where the table should be moved.", "type": "string"}, "tableName": {"description": "Required. The name of the table to be moved.", "type": "string"}}, "type": "object"}, "MoveTableToDatabaseResponse": {"description": "Response message for DataprocMetastore.MoveTableToDatabase.", "id": "MoveTableToDatabaseResponse", "properties": {}, "type": "object"}, "MultiRegionMetadata": {"description": "The metadata for the multi-region that includes the constituent regions. The metadata is only populated if the region is multi-region. For single region or custom dual region, it will be empty.", "id": "MultiRegionMetadata", "properties": {"constituentRegions": {"description": "The regions constituting the multi-region.", "items": {"type": "string"}, "type": "array"}, "continent": {"description": "The continent for this multi-region.", "type": "string"}, "witnessRegion": {"description": "The Spanner witness region for this multi-region.", "type": "string"}}, "type": "object"}, "NetworkConfig": {"description": "Network configuration for the Dataproc Metastore service.", "id": "NetworkConfig", "properties": {"consumers": {"description": "Immutable. The consumer-side network configuration for the Dataproc Metastore instance.", "items": {"$ref": "Consumer"}, "type": "array"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is false, it means the operation is still in progress. If true, the operation is completed, and either error or response is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the name should be a resource name ending with operations/{unique_id}.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as Delete, the response is google.protobuf.Empty. If the original method is standard Get/Create/Update, the response should be the resource. For other methods, the response should have the type XxxResponse, where Xxx is the original method name. For example, if the original method name is TakeSnapshot(), the inferred response type is TakeSnapshotResponse.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of a long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the caller has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of 1, corresponding to Code.CANCELLED.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources.A Policy is a collection of bindings. A binding binds one or more members, or principals, to a single role. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A role is a named list of permissions; each role can be an IAM predefined role or a user-created custom role.For some types of Google Cloud resources, a binding can also specify a condition, which is a logical expression that allows access to a resource only if the expression evaluates to true. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the IAM documentation (https://cloud.google.com/iam/help/conditions/resource-policies).JSON example: { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } YAML example: bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 For a description of IAM and its features, see the IAM documentation (https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of members, or principals, with a role. Optionally, may specify a condition that determines how and when the bindings are applied. Each of the bindings must contain at least one principal.The bindings in a Policy can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the bindings grant 50 different roles to user:<EMAIL>, and not to any other principal, then you can add another 1,450 principals to the bindings in the Policy.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "etag is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the etag in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An etag is returned in the response to getIamPolicy, and systems are expected to put that etag in the request to setIamPolicy to ensure that their change will be applied to the same version of the policy.Important: If you use IAM Conditions, you must include the etag field whenever you call setIamPolicy. If you omit this field, then IAM allows you to overwrite a version 3 policy with a version 1 policy, and all of the conditions in the version 3 policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy.Valid values are 0, 1, and 3. Requests that specify an invalid value are rejected.Any operation that affects conditional role bindings must specify version 3. This requirement applies to the following operations: Getting a policy that includes a conditional role binding Adding a conditional role binding to a policy Changing a conditional role binding in a policy Removing any role binding, with or without a condition, from a policy that includes conditionsImportant: If you use IAM Conditions, you must include the etag field whenever you call setIamPolicy. If you omit this field, then IAM allows you to overwrite a version 3 policy with a version 1 policy, and all of the conditions in the version 3 policy are lost.If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset.To learn which resources support conditions in their IAM policies, see the IAM documentation (https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "QueryMetadataRequest": {"description": "Request message for DataprocMetastore.QueryMetadata.", "id": "QueryMetadataRequest", "properties": {"query": {"description": "Required. A read-only SQL query to execute against the metadata database. The query cannot change or mutate the data.", "type": "string"}}, "type": "object"}, "QueryMetadataResponse": {"description": "Response message for DataprocMetastore.QueryMetadata.", "id": "QueryMetadataResponse", "properties": {"resultManifestUri": {"description": "The manifest URI is link to a JSON instance in Cloud Storage. This instance manifests immediately along with QueryMetadataResponse. The content of the URI is not retriable until the long-running operation query against the metadata finishes.", "type": "string"}}, "type": "object"}, "Restore": {"description": "The details of a metadata restore operation.", "id": "Rest<PERSON>", "properties": {"backup": {"description": "Output only. The relative resource name of the metastore service backup to restore from, in the following form:projects/{project_id}/locations/{location_id}/services/{service_id}/backups/{backup_id}.", "readOnly": true, "type": "string"}, "backupLocation": {"description": "Optional. A Cloud Storage URI specifying where the backup artifacts are stored, in the format gs:///.", "type": "string"}, "details": {"description": "Output only. The restore details containing the revision of the service to be restored to, in format of JSON.", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time when the restore ended.", "format": "google-datetime", "readOnly": true, "type": "string"}, "startTime": {"description": "Output only. The time when the restore started.", "format": "google-datetime", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the restore.", "enum": ["STATE_UNSPECIFIED", "RUNNING", "SUCCEEDED", "FAILED", "CANCELLED"], "enumDescriptions": ["The state of the metadata restore is unknown.", "The metadata restore is running.", "The metadata restore completed successfully.", "The metadata restore failed.", "The metadata restore is cancelled."], "readOnly": true, "type": "string"}, "type": {"description": "Output only. The type of restore.", "enum": ["RESTORE_TYPE_UNSPECIFIED", "FULL", "METADATA_ONLY"], "enumDescriptions": ["The restore type is unknown.", "The service's metadata and configuration are restored.", "Only the service's metadata is restored."], "readOnly": true, "type": "string"}}, "type": "object"}, "RestoreServiceRequest": {"description": "Request message for DataprocMetastore.RestoreService.", "id": "RestoreServiceRequest", "properties": {"backup": {"description": "Optional. The relative resource name of the metastore service backup to restore from, in the following form:projects/{project_id}/locations/{location_id}/services/{service_id}/backups/{backup_id}. Mutually exclusive with backup_location, and exactly one of the two must be set.", "type": "string"}, "backupLocation": {"description": "Optional. A Cloud Storage URI specifying the location of the backup artifacts, namely - backup avro files under \"avro/\", backup_metastore.json and service.json, in the following form:gs://. Mutually exclusive with backup, and exactly one of the two must be set.", "type": "string"}, "requestId": {"description": "Optional. A request ID. Specify a unique request ID to allow the server to ignore the request if it has completed. The server will ignore subsequent requests that provide a duplicate request ID for at least 60 minutes after the first request.For example, if an initial request times out, followed by another request with the same request ID, the server ignores the second request to prevent the creation of duplicate commitments.The request ID must be a valid UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format). A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.", "type": "string"}, "restoreType": {"description": "Optional. The type of restore. If unspecified, defaults to METADATA_ONLY.", "enum": ["RESTORE_TYPE_UNSPECIFIED", "FULL", "METADATA_ONLY"], "enumDescriptions": ["The restore type is unknown.", "The service's metadata and configuration are restored.", "Only the service's metadata is restored."], "type": "string"}}, "type": "object"}, "ScalingConfig": {"description": "Represents the scaling configuration of a metastore service.", "id": "ScalingConfig", "properties": {"autoscalingConfig": {"$ref": "AutoscalingConfig", "description": "Optional. The autoscaling configuration."}, "instanceSize": {"description": "An enum of readable instance sizes, with each instance size mapping to a float value (e.g. InstanceSize.EXTRA_SMALL = scaling_factor(0.1))", "enum": ["INSTANCE_SIZE_UNSPECIFIED", "EXTRA_SMALL", "SMALL", "MEDIUM", "LARGE", "EXTRA_LARGE"], "enumDescriptions": ["Unspecified instance size", "Extra small instance size, maps to a scaling factor of 0.1.", "Small instance size, maps to a scaling factor of 0.5.", "Medium instance size, maps to a scaling factor of 1.0.", "Large instance size, maps to a scaling factor of 3.0.", "Extra large instance size, maps to a scaling factor of 6.0."], "type": "string"}, "scalingFactor": {"description": "Scaling factor, increments of 0.1 for values less than 1.0, and increments of 1.0 for values greater than 1.0.", "format": "float", "type": "number"}}, "type": "object"}, "ScheduledBackup": {"description": "This specifies the configuration of scheduled backup.", "id": "ScheduledBackup", "properties": {"backupLocation": {"description": "Optional. A Cloud Storage URI of a folder, in the format gs:///. A sub-folder containing backup files will be stored below it.", "type": "string"}, "cronSchedule": {"description": "Optional. The scheduled interval in Cron format, see https://en.wikipedia.org/wiki/Cron The default is empty: scheduled backup is not enabled. Must be specified to enable scheduled backups.", "type": "string"}, "enabled": {"description": "Optional. Defines whether the scheduled backup is enabled. The default value is false.", "type": "boolean"}, "latestBackup": {"$ref": "LatestBackup", "description": "Output only. The details of the latest scheduled backup.", "readOnly": true}, "nextScheduledTime": {"description": "Output only. The time when the next backups execution is scheduled to start.", "format": "google-datetime", "readOnly": true, "type": "string"}, "timeZone": {"description": "Optional. Specifies the time zone to be used when interpreting cron_schedule. Must be a time zone name from the time zone database (https://en.wikipedia.org/wiki/List_of_tz_database_time_zones), e.g. America/Los_Angeles or Africa/Abidjan. If left unspecified, the default is UTC.", "type": "string"}}, "type": "object"}, "Secret": {"description": "A securely stored value.", "id": "Secret", "properties": {"cloudSecret": {"description": "Optional. The relative resource name of a Secret Manager secret version, in the following form:projects/{project_number}/secrets/{secret_id}/versions/{version_id}.", "type": "string"}}, "type": "object"}, "Service": {"description": "A managed metastore service that serves metadata queries.", "id": "Service", "properties": {"artifactGcsUri": {"description": "Output only. A Cloud Storage URI (starting with gs://) that specifies where artifacts related to the metastore service are stored.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time when the metastore service was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "databaseType": {"description": "Immutable. The database type that the Metastore service stores its data.", "enum": ["DATABASE_TYPE_UNSPECIFIED", "MYSQL", "SPANNER"], "enumDescriptions": ["The DATABASE_TYPE is not set.", "MySQL is used to persist the metastore data.", "Spanner is used to persist the metastore data."], "type": "string"}, "deletionProtection": {"description": "Optional. Indicates if the dataproc metastore should be protected against accidental deletions.", "type": "boolean"}, "encryptionConfig": {"$ref": "EncryptionConfig", "description": "Immutable. Information used to configure the Dataproc Metastore service to encrypt customer data at rest. Cannot be updated."}, "endpointUri": {"description": "Output only. The URI of the endpoint used to access the metastore service.", "readOnly": true, "type": "string"}, "hiveMetastoreConfig": {"$ref": "HiveMetastoreConfig", "description": "Configuration information specific to running Hive metastore software as the metastore service."}, "labels": {"additionalProperties": {"type": "string"}, "description": "User-defined labels for the metastore service.", "type": "object"}, "maintenanceWindow": {"$ref": "MaintenanceWindow", "description": "Optional. The one hour maintenance window of the metastore service. This specifies when the service can be restarted for maintenance purposes in UTC time. Maintenance window is not needed for services with the SPANNER database type."}, "metadataIntegration": {"$ref": "MetadataIntegration", "description": "Optional. The setting that defines how metastore metadata should be integrated with external services and systems."}, "metadataManagementActivity": {"$ref": "MetadataManagementActivity", "description": "Output only. The metadata management activities of the metastore service.", "readOnly": true}, "name": {"description": "Immutable. Identifier. The relative resource name of the metastore service, in the following format:projects/{project_number}/locations/{location_id}/services/{service_id}.", "type": "string"}, "network": {"description": "Immutable. The relative resource name of the VPC network on which the instance can be accessed. It is specified in the following form:projects/{project_number}/global/networks/{network_id}.", "type": "string"}, "networkConfig": {"$ref": "NetworkConfig", "description": "Optional. The configuration specifying the network settings for the Dataproc Metastore service."}, "port": {"description": "Optional. The TCP port at which the metastore service is reached. Default: 9083.", "format": "int32", "type": "integer"}, "releaseChannel": {"description": "Immutable. The release channel of the service. If unspecified, defaults to STABLE.", "enum": ["RELEASE_CHANNEL_UNSPECIFIED", "CANARY", "STABLE"], "enumDescriptions": ["Release channel is not specified.", "The CANARY release channel contains the newest features, which may be unstable and subject to unresolved issues with no known workarounds. Services using the CANARY release channel are not subject to any SLAs.", "The STABLE release channel contains features that are considered stable and have been validated for production use."], "type": "string"}, "scalingConfig": {"$ref": "ScalingConfig", "description": "Optional. Scaling configuration of the metastore service."}, "scheduledBackup": {"$ref": "ScheduledBackup", "description": "Optional. The configuration of scheduled backup for the metastore service."}, "state": {"description": "Output only. The current state of the metastore service.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "SUSPENDING", "SUSPENDED", "UPDATING", "DELETING", "ERROR", "AUTOSCALING", "MIGRATING"], "enumDescriptions": ["The state of the metastore service is unknown.", "The metastore service is in the process of being created.", "The metastore service is running and ready to serve queries.", "The metastore service is entering suspension. Its query-serving availability may cease unexpectedly.", "The metastore service is suspended and unable to serve queries.", "The metastore service is being updated. It remains usable but cannot accept additional update requests or be deleted at this time.", "The metastore service is undergoing deletion. It cannot be used.", "The metastore service has encountered an error and cannot be used. The metastore service should be deleted.", "The Dataproc Metastore service 2 is being scaled up or down.", "The metastore service is processing a managed migration."], "readOnly": true, "type": "string"}, "stateMessage": {"description": "Output only. Additional information about the current state of the metastore service, if available.", "readOnly": true, "type": "string"}, "telemetryConfig": {"$ref": "TelemetryConfig", "description": "Optional. The configuration specifying telemetry settings for the Dataproc Metastore service. If unspecified defaults to JSON."}, "tier": {"description": "Optional. The tier of the service.", "enum": ["TIER_UNSPECIFIED", "DEVELOPER", "ENTERPRISE"], "enumDescriptions": ["The tier is not set.", "The developer tier provides limited scalability and no fault tolerance. Good for low-cost proof-of-concept.", "The enterprise tier provides multi-zone high availability, and sufficient scalability for enterprise-level Dataproc Metastore workloads."], "type": "string"}, "uid": {"description": "Output only. The globally unique resource identifier of the metastore service.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time when the metastore service was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for SetIamPolicy method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the resource. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used:paths: \"bindings, etag\"", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "StartMigrationRequest": {"description": "Request message for DataprocMetastore.StartMigration.", "id": "StartMigrationRequest", "properties": {"migrationExecution": {"$ref": "MigrationExecution", "description": "Required. The configuration details for the migration."}, "requestId": {"description": "Optional. A request ID. Specify a unique request ID to allow the server to ignore the request if it has completed. The server will ignore subsequent requests that provide a duplicate request ID for at least 60 minutes after the first request.For example, if an initial request times out, followed by another request with the same request ID, the server ignores the second request to prevent the creation of duplicate commitments.The request ID must be a valid UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier#Format) A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.", "type": "string"}}, "type": "object"}, "Status": {"description": "The Status type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by gRPC (https://github.com/grpc). Each Status message contains three pieces of data: error code, error message, and error details.You can find out more about this error model and how to work with it in the API Design Guide (https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StatusProto": {"description": "Wire-format for a Status object", "id": "StatusProto", "properties": {"canonicalCode": {"description": "copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional int32 canonical_code = 6;", "format": "int32", "type": "integer"}, "code": {"description": "Numeric code drawn from the space specified below. Often, this is the canonical error space, and code is drawn from google3/util/task/codes.proto copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional int32 code = 1;", "format": "int32", "type": "integer"}, "message": {"description": "Detail message copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional string message = 3;", "type": "string"}, "messageSet": {"$ref": "MessageSet", "description": "message_set associates an arbitrary proto message with the status. copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional proto2.bridge.MessageSet message_set = 5;"}, "space": {"description": "copybara:strip_begin(b/383363683) Space to which this status belongs copybara:strip_end_and_replace optional string space = 2; // Space to which this status belongs", "type": "string"}}, "type": "object"}, "TelemetryConfig": {"description": "Telemetry Configuration for the Dataproc Metastore service.", "id": "TelemetryConfig", "properties": {"logFormat": {"description": "Optional. The output format of the Dataproc Metastore service's logs.", "enum": ["LOG_FORMAT_UNSPECIFIED", "LEGACY", "JSON"], "enumDescriptions": ["The LOG_FORMAT is not set.", "Logging output uses the legacy textPayload format.", "Logging output uses the jsonPayload format."], "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for TestIamPermissions method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the resource. Permissions with wildcards (such as * or storage.*) are not allowed. For more information see IAM Overview (https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for TestIamPermissions method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of TestPermissionsRequest.permissions that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "Dataproc Metastore API", "version": "v1", "version_module": true}