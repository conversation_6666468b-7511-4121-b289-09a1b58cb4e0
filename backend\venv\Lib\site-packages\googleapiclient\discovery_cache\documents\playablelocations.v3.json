{"basePath": "", "baseUrl": "https://playablelocations.googleapis.com/", "batchPath": "batch", "canonicalName": "Playable Locations", "description": "", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/maps/contact-sales/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "playablelocations:v3", "kind": "discovery#restDescription", "mtlsRootUrl": "https://playablelocations.mtls.googleapis.com/", "name": "playablelocations", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"v3": {"methods": {"logImpressions": {"description": "Logs new events when playable locations are displayed, and when they are interacted with. Impressions are not partially saved; either all impressions are saved and this request succeeds, or no impressions are saved, and this request fails.", "flatPath": "v3:logImpressions", "httpMethod": "POST", "id": "playablelocations.logImpressions", "parameterOrder": [], "parameters": {}, "path": "v3:logImpressions", "request": {"$ref": "GoogleMapsPlayablelocationsV3LogImpressionsRequest"}, "response": {"$ref": "GoogleMapsPlayablelocationsV3LogImpressionsResponse"}}, "logPlayerReports": {"description": "Logs bad playable location reports submitted by players. Reports are not partially saved; either all reports are saved and this request succeeds, or no reports are saved, and this request fails.", "flatPath": "v3:logPlayerReports", "httpMethod": "POST", "id": "playablelocations.logPlayerReports", "parameterOrder": [], "parameters": {}, "path": "v3:logPlayerReports", "request": {"$ref": "GoogleMapsPlayablelocationsV3LogPlayerReportsRequest"}, "response": {"$ref": "GoogleMapsPlayablelocationsV3LogPlayerReportsResponse"}}, "samplePlayableLocations": {"description": "Returns a set of playable locations that lie within a specified area, that satisfy optional filter criteria. Note: Identical `SamplePlayableLocations` requests can return different results as the state of the world changes over time.", "flatPath": "v3:samplePlayableLocations", "httpMethod": "POST", "id": "playablelocations.samplePlayableLocations", "parameterOrder": [], "parameters": {}, "path": "v3:samplePlayableLocations", "request": {"$ref": "GoogleMapsPlayablelocationsV3SamplePlayableLocationsRequest"}, "response": {"$ref": "GoogleMapsPlayablelocationsV3SamplePlayableLocationsResponse"}}}}}, "revision": "20211008", "rootUrl": "https://playablelocations.googleapis.com/", "schemas": {"GoogleMapsPlayablelocationsV3Impression": {"description": "Encapsulates impression event details.", "id": "GoogleMapsPlayablelocationsV3Impression", "properties": {"gameObjectType": {"description": "An arbitrary, developer-defined type identifier for each type of game object used in your game. Since players interact with differ types of game objects in different ways, this field allows you to segregate impression data by type for analysis. You should assign a unique `game_object_type` ID to represent a distinct type of game object in your game. For example, 1=monster location, 2=powerup location.", "format": "int32", "type": "integer"}, "impressionType": {"description": "Required. The type of impression event.", "enum": ["IMPRESSION_TYPE_UNSPECIFIED", "PRESENTED", "INTERACTED"], "enumDescriptions": ["Unspecified type. Do not use.", "The playable location was presented to a player.", "A player interacted with the playable location."], "type": "string"}, "locationName": {"description": "Required. The name of the playable location.", "type": "string"}}, "type": "object"}, "GoogleMapsPlayablelocationsV3LogImpressionsRequest": {"description": "A request for logging impressions.", "id": "GoogleMapsPlayablelocationsV3LogImpressionsRequest", "properties": {"clientInfo": {"$ref": "GoogleMapsUnityClientInfo", "description": "Required. Information about the client device. For example, device model and operating system."}, "impressions": {"description": "Required. Impression event details. The maximum number of impression reports that you can log at once is 50.", "items": {"$ref": "GoogleMapsPlayablelocationsV3Impression"}, "type": "array"}, "requestId": {"description": "Required. A string that uniquely identifies the log impressions request. This allows you to detect duplicate requests. We recommend that you use UUIDs for this value. The value must not exceed 50 characters. You should reuse the `request_id` only when retrying a request in case of failure. In this case, the request must be identical to the one that failed.", "type": "string"}}, "type": "object"}, "GoogleMapsPlayablelocationsV3LogImpressionsResponse": {"description": "A response for the LogImpressions method. This method returns no data upon success.", "id": "GoogleMapsPlayablelocationsV3LogImpressionsResponse", "properties": {}, "type": "object"}, "GoogleMapsPlayablelocationsV3LogPlayerReportsRequest": {"description": "A request for logging your player's bad location reports.", "id": "GoogleMapsPlayablelocationsV3LogPlayerReportsRequest", "properties": {"clientInfo": {"$ref": "GoogleMapsUnityClientInfo", "description": "Required. Information about the client device (for example, device model and operating system)."}, "playerReports": {"description": "Required. Player reports. The maximum number of player reports that you can log at once is 50.", "items": {"$ref": "GoogleMapsPlayablelocationsV3PlayerReport"}, "type": "array"}, "requestId": {"description": "Required. A string that uniquely identifies the log player reports request. This allows you to detect duplicate requests. We recommend that you use UUIDs for this value. The value must not exceed 50 characters. You should reuse the `request_id` only when retrying a request in the case of a failure. In that case, the request must be identical to the one that failed.", "type": "string"}}, "type": "object"}, "GoogleMapsPlayablelocationsV3LogPlayerReportsResponse": {"description": "A response for the LogPlayerReports method. This method returns no data upon success.", "id": "GoogleMapsPlayablelocationsV3LogPlayerReportsResponse", "properties": {}, "type": "object"}, "GoogleMapsPlayablelocationsV3PlayerReport": {"description": "A report submitted by a player about a playable location that is considered inappropriate for use in the game.", "id": "GoogleMapsPlayablelocationsV3PlayerReport", "properties": {"languageCode": {"description": "Language code (in BCP-47 format) indicating the language of the freeform description provided in `reason_details`. Examples are \"en\", \"en-US\" or \"ja-Latn\". For more information, see http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.", "type": "string"}, "locationName": {"description": "Required. The name of the playable location.", "type": "string"}, "reasonDetails": {"description": "Required. A free-form description detailing why the playable location is considered bad.", "type": "string"}, "reasons": {"description": "Required. One or more reasons why this playable location is considered bad.", "items": {"enum": ["BAD_LOCATION_REASON_UNSPECIFIED", "OTHER", "NOT_PEDESTRIAN_ACCESSIBLE", "NOT_OPEN_TO_PUBLIC", "PERMANENTLY_CLOSED", "TEMPORARILY_INACCESSIBLE"], "enumDescriptions": ["Unspecified reason. Do not use.", "The reason isn't one of the reasons in this enumeration.", "The playable location isn't accessible to pedestrians. For example, if it's in the middle of a highway.", "The playable location isn't open to the public. For example, a private office building.", "The playable location is permanently closed. For example, when a business has been shut down.", "The playable location is temporarily inaccessible. For example, when a business has closed for renovations."], "type": "string"}, "type": "array"}}, "type": "object"}, "GoogleMapsPlayablelocationsV3SampleAreaFilter": {"description": "Specifies the area to search for playable locations.", "id": "GoogleMapsPlayablelocationsV3SampleAreaFilter", "properties": {"s2CellId": {"description": "Required. The S2 cell ID of the area you want. This must be between cell level 11 and 14 (inclusive). S2 cells are 64-bit integers that identify areas on the Earth. They are hierarchical, and can therefore be used for spatial indexing. The S2 geometry library is available in a number of languages: * [C++](https://github.com/google/s2geometry) * [Java](https://github.com/google/s2-geometry-library-java) * [Go](https://github.com/golang/geo) * [Python](https://github.com/google/s2geometry/tree/master/src/python)", "format": "uint64", "type": "string"}}, "type": "object"}, "GoogleMapsPlayablelocationsV3SampleCriterion": {"description": "Encapsulates a filter criterion for searching for a set of playable locations.", "id": "GoogleMapsPlayablelocationsV3SampleCriterion", "properties": {"fieldsToReturn": {"description": "Specifies which `PlayableLocation` fields are returned. `name` (which is used for logging impressions), `center_point` and `place_id` (or `plus_code`) are always returned. The following fields are omitted unless you specify them here: * snapped_point * types Note: The more fields you include, the more expensive in terms of data and associated latency your query will be.", "format": "google-fieldmask", "type": "string"}, "filter": {"$ref": "GoogleMapsPlayablelocationsV3SampleFilter", "description": "Specifies filtering options, and specifies what will be included in the result set."}, "gameObjectType": {"description": "Required. An arbitrary, developer-defined identifier of the type of game object that the playable location is used for. This field allows you to specify criteria per game object type when searching for playable locations. You should assign a unique `game_object_type` ID across all `request_criteria` to represent a distinct type of game object. For example, 1=monster location, 2=powerup location. The response contains a map.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleMapsPlayablelocationsV3SampleFilter": {"description": "Specifies the filters to use when searching for playable locations.", "id": "GoogleMapsPlayablelocationsV3SampleFilter", "properties": {"includedTypes": {"description": "Restricts the set of playable locations to just the [types](/maps/documentation/gaming/tt/types) that you want.", "items": {"type": "string"}, "type": "array"}, "maxLocationCount": {"description": "Specifies the maximum number of playable locations to return. This value must not be greater than 1000. The default value is 100. Only the top-ranking playable locations are returned.", "format": "int32", "type": "integer"}, "spacing": {"$ref": "GoogleMapsPlayablelocationsV3SampleSpacingOptions", "description": "A set of options that control the spacing between playable locations. By default the minimum distance between locations is 200m."}}, "type": "object"}, "GoogleMapsPlayablelocationsV3SamplePlayableLocation": {"description": "A geographical point suitable for placing game objects in location-based games.", "id": "GoogleMapsPlayablelocationsV3SamplePlayableLocation", "properties": {"centerPoint": {"$ref": "GoogleTypeLatLng", "description": "Required. The latitude and longitude associated with the center of the playable location. By default, the set of playable locations returned from SamplePlayableLocations use center-point coordinates."}, "name": {"description": "Required. The name of this playable location.", "type": "string"}, "placeId": {"description": "A [place ID] (https://developers.google.com/places/place-id)", "type": "string"}, "plusCode": {"description": "A [plus code] (http://openlocationcode.com)", "type": "string"}, "snappedPoint": {"$ref": "GoogleTypeLatLng", "description": "The playable location's coordinates, snapped to the sidewalk of the nearest road, if a nearby road exists."}, "types": {"description": "A collection of [Playable Location Types](/maps/documentation/gaming/tt/types) for this playable location. The first type in the collection is the primary type. Type information might not be available for all playable locations.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleMapsPlayablelocationsV3SamplePlayableLocationList": {"description": "A list of PlayableLocation objects that satisfies a single Criterion.", "id": "GoogleMapsPlayablelocationsV3SamplePlayableLocationList", "properties": {"locations": {"description": "A list of playable locations for this game object type.", "items": {"$ref": "GoogleMapsPlayablelocationsV3SamplePlayableLocation"}, "type": "array"}}, "type": "object"}, "GoogleMapsPlayablelocationsV3SamplePlayableLocationsRequest": {"description": " Life of a query: - When a game starts in a new location, your game server issues a SamplePlayableLocations request. The request specifies the S2 cell, and contains one or more \"criteria\" for filtering: - Criterion 0: i locations for long-lived bases, or level 0 monsters, or... - Criterion 1: j locations for short-lived bases, or level 1 monsters, ... - Criterion 2: k locations for random objects. - etc (up to 5 criterion may be specified). `PlayableLocationList` will then contain mutually exclusive lists of `PlayableLocation` objects that satisfy each of the criteria. Think of it as a collection of real-world locations that you can then associate with your game state. Note: These points are impermanent in nature. E.g, parks can close, and places can be removed. The response specifies how long you can expect the playable locations to last. Once they expire, you should query the `samplePlayableLocations` API again to get a fresh view of the real world.", "id": "GoogleMapsPlayablelocationsV3SamplePlayableLocationsRequest", "properties": {"areaFilter": {"$ref": "GoogleMapsPlayablelocationsV3SampleAreaFilter", "description": "Required. Specifies the area to search within for playable locations."}, "criteria": {"description": "Required. Specifies one or more (up to 5) criteria for filtering the returned playable locations.", "items": {"$ref": "GoogleMapsPlayablelocationsV3SampleCriterion"}, "type": "array"}}, "type": "object"}, "GoogleMapsPlayablelocationsV3SamplePlayableLocationsResponse": {"description": " Response for the SamplePlayableLocations method.", "id": "GoogleMapsPlayablelocationsV3SamplePlayableLocationsResponse", "properties": {"locationsPerGameObjectType": {"additionalProperties": {"$ref": "GoogleMapsPlayablelocationsV3SamplePlayableLocationList"}, "description": "Each PlayableLocation object corresponds to a game_object_type specified in the request.", "type": "object"}, "ttl": {"description": "Required. Specifies the \"time-to-live\" for the set of playable locations. You can use this value to determine how long to cache the set of playable locations. After this length of time, your back-end game server should issue a new SamplePlayableLocations request to get a fresh set of playable locations (because for example, they might have been removed, a park might have closed for the day, a business might have closed permanently).", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleMapsPlayablelocationsV3SampleSpacingOptions": {"description": "A set of options that specifies the separation between playable locations.", "id": "GoogleMapsPlayablelocationsV3SampleSpacingOptions", "properties": {"minSpacingMeters": {"description": "Required. The minimum spacing between any two playable locations, measured in meters. The minimum value is 30. The maximum value is 1000. Inputs will be rounded up to the next 10 meter interval. The default value is 200m. Set this field to remove tight clusters of playable locations. Note: The spacing is a greedy algorithm. It optimizes for selecting the highest ranking locations first, not to maximize the number of locations selected. Consider the following scenario: * Rank: A: 2, B: 1, C: 3. * Distance: A--200m--B--200m--C If spacing=250, it will pick the highest ranked location [B], not [A, C]. Note: Spacing works within the game object type itself, as well as the previous ones. Suppose three game object types, each with the following spacing: * X: 400m, Y: undefined, Z: 200m. 1. Add locations for X, within 400m of each other. 2. Add locations for Y, without any spacing. 3. Finally, add locations for Z within 200m of each other as well X and Y. The distance diagram between those locations end up as: * From->To. * X->X: 400m * Y->X, Y->Y: unspecified. * Z->X, Z->Y, Z->Z: 200m.", "format": "double", "type": "number"}, "pointType": {"description": "Specifies whether the minimum spacing constraint applies to the center-point or to the snapped point of playable locations. The default value is `CENTER_POINT`. If a snapped point is not available for a playable location, its center-point is used instead. Set this to the point type used in your game.", "enum": ["POINT_TYPE_UNSPECIFIED", "CENTER_POINT", "SNAPPED_POINT"], "enumDescriptions": ["Unspecified point type. Do not use this value.", "The geographic coordinates correspond to the center of the location.", "The geographic coordinates correspond to the location snapped to the sidewalk of the nearest road (when a nearby road exists)."], "type": "string"}}, "type": "object"}, "GoogleMapsUnityClientInfo": {"description": "Client information.", "id": "GoogleMapsUnityClientInfo", "properties": {"apiClient": {"description": "API client name and version. For example, the SDK calling the API. The exact format is up to the client.", "type": "string"}, "applicationId": {"description": "Application ID, such as the package name on Android and the bundle identifier on iOS platforms.", "type": "string"}, "applicationVersion": {"description": "Application version number, such as \"1.2.3\". The exact format is application-dependent.", "type": "string"}, "deviceModel": {"description": "Device model as reported by the device. The exact format is platform-dependent.", "type": "string"}, "languageCode": {"description": "Language code (in BCP-47 format) indicating the UI language of the client. Examples are \"en\", \"en-US\" or \"ja-Latn\". For more information, see http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.", "type": "string"}, "operatingSystem": {"description": "Operating system name and version as reported by the OS. For example, \"Mac OS X 10.10.4\". The exact format is platform-dependent.", "type": "string"}, "operatingSystemBuild": {"description": "Build number/version of the operating system. e.g., the contents of android.os.Build.ID in Android, or the contents of sysctl \"kern.osversion\" in iOS.", "type": "string"}, "platform": {"description": "Platform where the application is running.", "enum": ["PLATFORM_UNSPECIFIED", "EDITOR", "MAC_OS", "WINDOWS", "LINUX", "ANDROID", "IOS", "WEB_GL"], "enumDescriptions": ["Unspecified or unknown OS.", "Development environment.", "macOS.", "Windows.", "Linux", "Android", "iOS", "WebGL."], "type": "string"}}, "type": "object"}, "GoogleTypeLatLng": {"description": "An object that represents a latitude/longitude pair. This is expressed as a pair of doubles to represent degrees latitude and degrees longitude. Unless specified otherwise, this object must conform to the WGS84 standard. Values must be within normalized ranges.", "id": "GoogleTypeLatLng", "properties": {"latitude": {"description": "The latitude in degrees. It must be in the range [-90.0, +90.0].", "format": "double", "type": "number"}, "longitude": {"description": "The longitude in degrees. It must be in the range [-180.0, +180.0].", "format": "double", "type": "number"}}, "type": "object"}}, "servicePath": "", "title": "Playable Locations API", "version": "v3", "version_module": true}