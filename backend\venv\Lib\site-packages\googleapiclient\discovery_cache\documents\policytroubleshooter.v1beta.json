{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://policytroubleshooter.googleapis.com/", "batchPath": "batch", "canonicalName": "Policy Troubleshooter", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/iam/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "policytroubleshooter:v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://policytroubleshooter.mtls.googleapis.com/", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"iam": {"methods": {"troubleshoot": {"description": "Checks whether a member has a specific permission for a specific resource, and explains why the member does or does not have that permission.", "flatPath": "v1beta/iam:troubleshoot", "httpMethod": "POST", "id": "policytroubleshooter.iam.troubleshoot", "parameterOrder": [], "parameters": {}, "path": "v1beta/iam:troubleshoot", "request": {"$ref": "GoogleCloudPolicytroubleshooterV1betaTroubleshootIamPolicyRequest"}, "response": {"$ref": "GoogleCloudPolicytroubleshooterV1betaTroubleshootIamPolicyResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}, "revision": "20240707", "rootUrl": "https://policytroubleshooter.googleapis.com/", "schemas": {"GoogleCloudPolicytroubleshooterV1betaAccessTuple": {"description": "Information about the member, resource, and permission to check.", "id": "GoogleCloudPolicytroubleshooterV1betaAccessTuple", "properties": {"fullResourceName": {"description": "Required. The full resource name that identifies the resource. For example, `//compute.googleapis.com/projects/my-project/zones/us-central1-a/instances/my-instance`. For examples of full resource names for Google Cloud services, see https://cloud.google.com/iam/help/troubleshooter/full-resource-names.", "type": "string"}, "permission": {"description": "Required. The IAM permission to check for the specified member and resource. For a complete list of IAM permissions, see https://cloud.google.com/iam/help/permissions/reference. For a complete list of predefined IAM roles and the permissions in each role, see https://cloud.google.com/iam/help/roles/reference.", "type": "string"}, "principal": {"description": "Required. The member, or principal, whose access you want to check, in the form of the email address that represents that member. For example, `<EMAIL>` or `<EMAIL>`. The member must be a Google Account or a service account. Other types of members are not supported.", "type": "string"}}, "type": "object"}, "GoogleCloudPolicytroubleshooterV1betaBindingExplanation": {"description": "Details about how a binding in a policy affects a member's ability to use a permission.", "id": "GoogleCloudPolicytroubleshooterV1betaBindingExplanation", "properties": {"access": {"description": "Indicates whether _this binding_ provides the specified permission to the specified member for the specified resource. This field does _not_ indicate whether the member actually has the permission for the resource. There might be another binding that overrides this binding. To determine whether the member actually has the permission, use the `access` field in the TroubleshootIamPolicyResponse.", "enum": ["ACCESS_STATE_UNSPECIFIED", "GRANTED", "NOT_GRANTED", "UNKNOWN_CONDITIONAL", "UNKNOWN_INFO_DENIED"], "enumDescriptions": ["Reserved for future use.", "The member has the permission.", "The member does not have the permission.", "The member has the permission only if a condition expression evaluates to `true`.", "The sender of the request does not have access to all of the policies that Policy Troubleshooter needs to evaluate."], "type": "string"}, "condition": {"$ref": "GoogleTypeExpr", "description": "A condition expression that prevents access unless the expression evaluates to `true`. To learn about IAM Conditions, see https://cloud.google.com/iam/help/conditions/overview."}, "memberships": {"additionalProperties": {"$ref": "GoogleCloudPolicytroubleshooterV1betaBindingExplanationAnnotatedMembership"}, "description": "Indicates whether each member in the binding includes the member specified in the request, either directly or indirectly. Each key identifies a member in the binding, and each value indicates whether the member in the binding includes the member in the request. For example, suppose that a binding includes the following members: * `user:<EMAIL>` * `group:<EMAIL>` You want to troubleshoot access for `user:<EMAIL>`. This user is a member of the group `group:<EMAIL>`. For the first member in the binding, the key is `user:<EMAIL>`, and the `membership` field in the value is set to `MEMBERSHIP_NOT_INCLUDED`. For the second member in the binding, the key is `group:<EMAIL>`, and the `membership` field in the value is set to `MEMBERSHIP_INCLUDED`.", "type": "object"}, "relevance": {"description": "The relevance of this binding to the overall determination for the entire policy.", "enum": ["HEURISTIC_RELEVANCE_UNSPECIFIED", "NORMAL", "HIGH"], "enumDescriptions": ["Reserved for future use.", "The data point has a limited effect on the result. Changing the data point is unlikely to affect the overall determination.", "The data point has a strong effect on the result. Changing the data point is likely to affect the overall determination."], "type": "string"}, "role": {"description": "The role that this binding grants. For example, `roles/compute.serviceAgent`. For a complete list of predefined IAM roles, as well as the permissions in each role, see https://cloud.google.com/iam/help/roles/reference.", "type": "string"}, "rolePermission": {"description": "Indicates whether the role granted by this binding contains the specified permission.", "enum": ["ROLE_PERMISSION_UNSPECIFIED", "ROLE_PERMISSION_INCLUDED", "ROLE_PERMISSION_NOT_INCLUDED", "ROLE_PERMISSION_UNKNOWN_INFO_DENIED"], "enumDescriptions": ["Reserved for future use.", "The permission is included in the role.", "The permission is not included in the role.", "The sender of the request is not allowed to access the binding."], "type": "string"}, "rolePermissionRelevance": {"description": "The relevance of the permission's existence, or nonexistence, in the role to the overall determination for the entire policy.", "enum": ["HEURISTIC_RELEVANCE_UNSPECIFIED", "NORMAL", "HIGH"], "enumDescriptions": ["Reserved for future use.", "The data point has a limited effect on the result. Changing the data point is unlikely to affect the overall determination.", "The data point has a strong effect on the result. Changing the data point is likely to affect the overall determination."], "type": "string"}}, "type": "object"}, "GoogleCloudPolicytroubleshooterV1betaBindingExplanationAnnotatedMembership": {"description": "Details about whether the binding includes the member.", "id": "GoogleCloudPolicytroubleshooterV1betaBindingExplanationAnnotatedMembership", "properties": {"membership": {"description": "Indicates whether the binding includes the member.", "enum": ["MEMBERSHIP_UNSPECIFIED", "MEMBERSHIP_INCLUDED", "MEMBERSHIP_NOT_INCLUDED", "MEMBERSHIP_UNKNOWN_INFO_DENIED", "MEMBERSHIP_UNKNOWN_UNSUPPORTED"], "enumDescriptions": ["Reserved for future use.", "The binding includes the member. The member can be included directly or indirectly. For example: * A member is included directly if that member is listed in the binding. * A member is included indirectly if that member is in a Google group or G Suite domain that is listed in the binding.", "The binding does not include the member.", "The sender of the request is not allowed to access the binding.", "The member is an unsupported type. Only Google Accounts and service accounts are supported."], "type": "string"}, "relevance": {"description": "The relevance of the member's status to the overall determination for the binding.", "enum": ["HEURISTIC_RELEVANCE_UNSPECIFIED", "NORMAL", "HIGH"], "enumDescriptions": ["Reserved for future use.", "The data point has a limited effect on the result. Changing the data point is unlikely to affect the overall determination.", "The data point has a strong effect on the result. Changing the data point is likely to affect the overall determination."], "type": "string"}}, "type": "object"}, "GoogleCloudPolicytroubleshooterV1betaExplainedPolicy": {"description": "Details about how a specific IAM Policy contributed to the access check.", "id": "GoogleCloudPolicytroubleshooterV1betaExplainedPolicy", "properties": {"access": {"description": "Indicates whether _this policy_ provides the specified permission to the specified member for the specified resource. This field does _not_ indicate whether the member actually has the permission for the resource. There might be another policy that overrides this policy. To determine whether the member actually has the permission, use the `access` field in the TroubleshootIamPolicyResponse.", "enum": ["ACCESS_STATE_UNSPECIFIED", "GRANTED", "NOT_GRANTED", "UNKNOWN_CONDITIONAL", "UNKNOWN_INFO_DENIED"], "enumDescriptions": ["Reserved for future use.", "The member has the permission.", "The member does not have the permission.", "The member has the permission only if a condition expression evaluates to `true`.", "The sender of the request does not have access to all of the policies that Policy Troubleshooter needs to evaluate."], "type": "string"}, "bindingExplanations": {"description": "Details about how each binding in the policy affects the member's ability, or inability, to use the permission for the resource. If the sender of the request does not have access to the policy, this field is omitted.", "items": {"$ref": "GoogleCloudPolicytroubleshooterV1betaBindingExplanation"}, "type": "array"}, "fullResourceName": {"description": "The full resource name that identifies the resource. For example, `//compute.googleapis.com/projects/my-project/zones/us-central1-a/instances/my-instance`. If the sender of the request does not have access to the policy, this field is omitted. For examples of full resource names for Google Cloud services, see https://cloud.google.com/iam/help/troubleshooter/full-resource-names.", "type": "string"}, "policy": {"$ref": "GoogleIamV1Policy", "description": "The IAM policy attached to the resource. If the sender of the request does not have access to the policy, this field is empty."}, "relevance": {"description": "The relevance of this policy to the overall determination in the TroubleshootIamPolicyResponse. If the sender of the request does not have access to the policy, this field is omitted.", "enum": ["HEURISTIC_RELEVANCE_UNSPECIFIED", "NORMAL", "HIGH"], "enumDescriptions": ["Reserved for future use.", "The data point has a limited effect on the result. Changing the data point is unlikely to affect the overall determination.", "The data point has a strong effect on the result. Changing the data point is likely to affect the overall determination."], "type": "string"}}, "type": "object"}, "GoogleCloudPolicytroubleshooterV1betaTroubleshootIamPolicyRequest": {"description": "Request for TroubleshootIamPolicy.", "id": "GoogleCloudPolicytroubleshooterV1betaTroubleshootIamPolicyRequest", "properties": {"accessTuple": {"$ref": "GoogleCloudPolicytroubleshooterV1betaAccessTuple", "description": "The information to use for checking whether a member has a permission for a resource."}}, "type": "object"}, "GoogleCloudPolicytroubleshooterV1betaTroubleshootIamPolicyResponse": {"description": "Response for TroubleshootIamPolicy.", "id": "GoogleCloudPolicytroubleshooterV1betaTroubleshootIamPolicyResponse", "properties": {"access": {"description": "Indicates whether the member has the specified permission for the specified resource, based on evaluating all of the applicable policies.", "enum": ["ACCESS_STATE_UNSPECIFIED", "GRANTED", "NOT_GRANTED", "UNKNOWN_CONDITIONAL", "UNKNOWN_INFO_DENIED"], "enumDescriptions": ["Reserved for future use.", "The member has the permission.", "The member does not have the permission.", "The member has the permission only if a condition expression evaluates to `true`.", "The sender of the request does not have access to all of the policies that Policy Troubleshooter needs to evaluate."], "type": "string"}, "explainedPolicies": {"description": "List of IAM policies that were evaluated to check the member's permissions, with annotations to indicate how each policy contributed to the final result. The list of policies can include the policy for the resource itself. It can also include policies that are inherited from higher levels of the resource hierarchy, including the organization, the folder, and the project. To learn more about the resource hierarchy, see https://cloud.google.com/iam/help/resource-hierarchy.", "items": {"$ref": "GoogleCloudPolicytroubleshooterV1betaExplainedPolicy"}, "type": "array"}}, "type": "object"}, "GoogleIamV1AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "GoogleIamV1AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "GoogleIamV1AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "GoogleIamV1AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "GoogleIamV1AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "GoogleIamV1Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "GoogleIamV1Binding", "properties": {"condition": {"$ref": "GoogleTypeExpr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "GoogleIamV1Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "GoogleIamV1Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "GoogleIamV1AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "GoogleIamV1Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleTypeExpr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "GoogleTypeExpr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Policy Troubleshooter API", "version": "v1beta", "version_module": true}