{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://firebaseml.googleapis.com/", "batchPath": "batch", "canonicalName": "Firebase ML", "description": "Access custom machine learning models hosted via Firebase ML.", "discoveryVersion": "v1", "documentationLink": "https://firebase.google.com", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "firebaseml:v2beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://firebaseml.mtls.googleapis.com/", "name": "firebaseml", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"resources": {"publishers": {"resources": {"models": {"methods": {"countTokens": {"description": "Perform a token counting.", "flatPath": "v2beta/projects/{projectsId}/locations/{locationsId}/publishers/{publishersId}/models/{modelsId}:countTokens", "httpMethod": "POST", "id": "firebaseml.projects.locations.publishers.models.countTokens", "parameterOrder": ["endpoint"], "parameters": {"endpoint": {"description": "Required. The name of the Endpoint requested to perform token counting. Format: `projects/{project}/locations/{location}/endpoints/{endpoint}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/publishers/[^/]+/models/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+endpoint}:countTokens", "request": {"$ref": "GoogleCloudAiplatformV1beta1CountTokensRequest"}, "response": {"$ref": "GoogleCloudAiplatformV1beta1CountTokensResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "generateContent": {"description": "Generate content with multimodal inputs.", "flatPath": "v2beta/projects/{projectsId}/locations/{locationsId}/publishers/{publishersId}/models/{modelsId}:generateContent", "httpMethod": "POST", "id": "firebaseml.projects.locations.publishers.models.generateContent", "parameterOrder": ["model"], "parameters": {"model": {"description": "Required. The fully qualified name of the publisher model or tuned model endpoint to use. Publisher model format: `projects/{project}/locations/{location}/publishers/*/models/*` Tuned model endpoint format: `projects/{project}/locations/{location}/endpoints/{endpoint}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/publishers/[^/]+/models/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+model}:generateContent", "request": {"$ref": "GoogleCloudAiplatformV1beta1GenerateContentRequest"}, "response": {"$ref": "GoogleCloudAiplatformV1beta1GenerateContentResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "streamGenerateContent": {"description": "Generate content with multimodal inputs with streaming support.", "flatPath": "v2beta/projects/{projectsId}/locations/{locationsId}/publishers/{publishersId}/models/{modelsId}:streamGenerateContent", "httpMethod": "POST", "id": "firebaseml.projects.locations.publishers.models.streamGenerateContent", "parameterOrder": ["model"], "parameters": {"model": {"description": "Required. The fully qualified name of the publisher model or tuned model endpoint to use. Publisher model format: `projects/{project}/locations/{location}/publishers/*/models/*` Tuned model endpoint format: `projects/{project}/locations/{location}/endpoints/{endpoint}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/publishers/[^/]+/models/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta/{+model}:streamGenerateContent", "request": {"$ref": "GoogleCloudAiplatformV1beta1GenerateContentRequest"}, "response": {"$ref": "GoogleCloudAiplatformV1beta1GenerateContentResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}, "revision": "20250618", "rootUrl": "https://firebaseml.googleapis.com/", "schemas": {"Date": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "Date", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1ApiAuth": {"description": "The generic reusable api auth config. Deprecated. Please use AuthConfig (google/cloud/aiplatform/master/auth.proto) instead.", "id": "GoogleCloudAiplatformV1beta1ApiAuth", "properties": {"apiKeyConfig": {"$ref": "GoogleCloudAiplatformV1beta1ApiAuthApiKeyConfig", "description": "The API secret."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1ApiAuthApiKeyConfig": {"description": "The API secret.", "id": "GoogleCloudAiplatformV1beta1ApiAuthApiKeyConfig", "properties": {"apiKeySecretVersion": {"description": "Required. The SecretManager secret version resource name storing API key. e.g. projects/{project}/secrets/{secret}/versions/{version}", "type": "string"}, "apiKeyString": {"description": "The API key string. Either this or `api_key_secret_version` must be set.", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1AuthConfig": {"description": "Auth configuration to run the extension.", "id": "GoogleCloudAiplatformV1beta1AuthConfig", "properties": {"apiKeyConfig": {"$ref": "GoogleCloudAiplatformV1beta1AuthConfigApiKeyConfig", "description": "Config for API key auth."}, "authType": {"description": "Type of auth scheme.", "enum": ["AUTH_TYPE_UNSPECIFIED", "NO_AUTH", "API_KEY_AUTH", "HTTP_BASIC_AUTH", "GOOGLE_SERVICE_ACCOUNT_AUTH", "OAUTH", "OIDC_AUTH"], "enumDescriptions": ["", "No Auth.", "API Key Auth.", "HTTP Basic Auth.", "Google Service Account Auth.", "<PERSON><PERSON><PERSON> auth.", "OpenID Connect (OIDC) Auth."], "type": "string"}, "googleServiceAccountConfig": {"$ref": "GoogleCloudAiplatformV1beta1AuthConfigGoogleServiceAccountConfig", "description": "Config for Google Service Account auth."}, "httpBasicAuthConfig": {"$ref": "GoogleCloudAiplatformV1beta1AuthConfigHttpBasicAuthConfig", "description": "Config for HTTP Basic auth."}, "oauthConfig": {"$ref": "GoogleCloudAiplatformV1beta1AuthConfigOauthConfig", "description": "Config for user oauth."}, "oidcConfig": {"$ref": "GoogleCloudAiplatformV1beta1AuthConfigOidcConfig", "description": "Config for user OIDC auth."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1AuthConfigApiKeyConfig": {"description": "Config for authentication with API key.", "id": "GoogleCloudAiplatformV1beta1AuthConfigApiKeyConfig", "properties": {"apiKeySecret": {"description": "Optional. The name of the SecretManager secret version resource storing the API key. Format: `projects/{project}/secrets/{secrete}/versions/{version}` - If both `api_key_secret` and `api_key_string` are specified, this field takes precedence over `api_key_string`. - If specified, the `secretmanager.versions.access` permission should be granted to Vertex AI Extension Service Agent (https://cloud.google.com/vertex-ai/docs/general/access-control#service-agents) on the specified resource.", "type": "string"}, "apiKeyString": {"description": "Optional. The API key to be used in the request directly.", "type": "string"}, "httpElementLocation": {"description": "Optional. The location of the API key.", "enum": ["HTTP_IN_UNSPECIFIED", "HTTP_IN_QUERY", "HTTP_IN_HEADER", "HTTP_IN_PATH", "HTTP_IN_BODY", "HTTP_IN_COOKIE"], "enumDescriptions": ["", "Element is in the HTTP request query.", "Element is in the HTTP request header.", "Element is in the HTTP request path.", "Element is in the HTTP request body.", "Element is in the HTTP request cookie."], "type": "string"}, "name": {"description": "Optional. The parameter name of the API key. E.g. If the API request is \"https://example.com/act?api_key=\", \"api_key\" would be the parameter name.", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1AuthConfigGoogleServiceAccountConfig": {"description": "Config for Google Service Account Authentication.", "id": "GoogleCloudAiplatformV1beta1AuthConfigGoogleServiceAccountConfig", "properties": {"serviceAccount": {"description": "Optional. The service account that the extension execution service runs as. - If the service account is specified, the `iam.serviceAccounts.getAccessToken` permission should be granted to Vertex AI Extension Service Agent (https://cloud.google.com/vertex-ai/docs/general/access-control#service-agents) on the specified service account. - If not specified, the Vertex AI Extension Service Agent will be used to execute the Extension.", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1AuthConfigHttpBasicAuthConfig": {"description": "Config for HTTP Basic Authentication.", "id": "GoogleCloudAiplatformV1beta1AuthConfigHttpBasicAuthConfig", "properties": {"credentialSecret": {"description": "Required. The name of the SecretManager secret version resource storing the base64 encoded credentials. Format: `projects/{project}/secrets/{secrete}/versions/{version}` - If specified, the `secretmanager.versions.access` permission should be granted to Vertex AI Extension Service Agent (https://cloud.google.com/vertex-ai/docs/general/access-control#service-agents) on the specified resource.", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1AuthConfigOauthConfig": {"description": "Config for user oauth.", "id": "GoogleCloudAiplatformV1beta1AuthConfigOauthConfig", "properties": {"accessToken": {"description": "Access token for extension endpoint. Only used to propagate token from [[ExecuteExtensionRequest.runtime_auth_config]] at request time.", "type": "string"}, "serviceAccount": {"description": "The service account used to generate access tokens for executing the Extension. - If the service account is specified, the `iam.serviceAccounts.getAccessToken` permission should be granted to Vertex AI Extension Service Agent (https://cloud.google.com/vertex-ai/docs/general/access-control#service-agents) on the provided service account.", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1AuthConfigOidcConfig": {"description": "Config for user OIDC auth.", "id": "GoogleCloudAiplatformV1beta1AuthConfigOidcConfig", "properties": {"idToken": {"description": "OpenID Connect formatted ID token for extension endpoint. Only used to propagate token from [[ExecuteExtensionRequest.runtime_auth_config]] at request time.", "type": "string"}, "serviceAccount": {"description": "The service account used to generate an OpenID Connect (OIDC)-compatible JWT token signed by the Google OIDC Provider (accounts.google.com) for extension endpoint (https://cloud.google.com/iam/docs/create-short-lived-credentials-direct#sa-credentials-oidc). - The audience for the token will be set to the URL in the server url defined in the OpenApi spec. - If the service account is provided, the service account should grant `iam.serviceAccounts.getOpenIdToken` permission to Vertex AI Extension Service Agent (https://cloud.google.com/vertex-ai/docs/general/access-control#service-agents).", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1Blob": {"description": "Content blob.", "id": "GoogleCloudAiplatformV1beta1Blob", "properties": {"data": {"description": "Required. Raw bytes.", "format": "byte", "type": "string"}, "displayName": {"description": "Optional. Display name of the blob. Used to provide a label or filename to distinguish blobs. This field is only returned in PromptMessage for prompt management. It is currently used in the Gemini GenerateContent calls only when server side tools (code_execution, google_search, and url_context) are enabled.", "type": "string"}, "mimeType": {"description": "Required. The IANA standard MIME type of the source data.", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1Candidate": {"description": "A response candidate generated from the model.", "id": "GoogleCloudAiplatformV1beta1Candidate", "properties": {"avgLogprobs": {"description": "Output only. Average log probability score of the candidate.", "format": "double", "readOnly": true, "type": "number"}, "citationMetadata": {"$ref": "GoogleCloudAiplatformV1beta1CitationMetadata", "description": "Output only. Source attribution of the generated content.", "readOnly": true}, "content": {"$ref": "GoogleCloudAiplatformV1beta1Content", "description": "Output only. Content parts of the candidate.", "readOnly": true}, "finishMessage": {"description": "Output only. Describes the reason the mode stopped generating tokens in more detail. This is only filled when `finish_reason` is set.", "readOnly": true, "type": "string"}, "finishReason": {"description": "Output only. The reason why the model stopped generating tokens. If empty, the model has not stopped generating the tokens.", "enum": ["FINISH_REASON_UNSPECIFIED", "STOP", "MAX_TOKENS", "SAFETY", "RECITATION", "OTHER", "BLOCKLIST", "PROHIBITED_CONTENT", "SPII", "MALFORMED_FUNCTION_CALL", "IMAGE_SAFETY", "IMAGE_PROHIBITED_CONTENT", "IMAGE_RECITATION", "IMAGE_OTHER", "UNEXPECTED_TOOL_CALL"], "enumDescriptions": ["The finish reason is unspecified.", "Token generation reached a natural stopping point or a configured stop sequence.", "Token generation reached the configured maximum output tokens.", "Token generation stopped because the content potentially contains safety violations. NOTE: When streaming, content is empty if content filters blocks the output.", "The token generation stopped because of potential recitation.", "All other reasons that stopped the token generation.", "Token generation stopped because the content contains forbidden terms.", "Token generation stopped for potentially containing prohibited content.", "Token generation stopped because the content potentially contains Sensitive Personally Identifiable Information (SPII).", "The function call generated by the model is invalid.", "Token generation stopped because generated images has safety violations.", "Image generation stopped because generated images has other prohibited content.", "Image generation stopped due to recitation.", "Image generation stopped because of other miscellaneous issue.", "The tool call generated by the model is invalid."], "readOnly": true, "type": "string"}, "groundingMetadata": {"$ref": "GoogleCloudAiplatformV1beta1GroundingMetadata", "description": "Output only. Metadata specifies sources used to ground generated content.", "readOnly": true}, "index": {"description": "Output only. Index of the candidate.", "format": "int32", "readOnly": true, "type": "integer"}, "logprobsResult": {"$ref": "GoogleCloudAiplatformV1beta1LogprobsResult", "description": "Output only. Log-likelihood scores for the response tokens and top tokens", "readOnly": true}, "safetyRatings": {"description": "Output only. List of ratings for the safety of a response candidate. There is at most one rating per category.", "items": {"$ref": "GoogleCloudAiplatformV1beta1SafetyRating"}, "readOnly": true, "type": "array"}, "urlContextMetadata": {"$ref": "GoogleCloudAiplatformV1beta1UrlContextMetadata", "description": "Output only. Metadata related to url context retrieval tool.", "readOnly": true}}, "type": "object"}, "GoogleCloudAiplatformV1beta1Citation": {"description": "Source attributions for content.", "id": "GoogleCloudAiplatformV1beta1Citation", "properties": {"endIndex": {"description": "Output only. End index into the content.", "format": "int32", "readOnly": true, "type": "integer"}, "license": {"description": "Output only. License of the attribution.", "readOnly": true, "type": "string"}, "publicationDate": {"$ref": "Date", "description": "Output only. Publication date of the attribution.", "readOnly": true}, "startIndex": {"description": "Output only. Start index into the content.", "format": "int32", "readOnly": true, "type": "integer"}, "title": {"description": "Output only. Title of the attribution.", "readOnly": true, "type": "string"}, "uri": {"description": "Output only. Url reference of the attribution.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1CitationMetadata": {"description": "A collection of source attributions for a piece of content.", "id": "GoogleCloudAiplatformV1beta1CitationMetadata", "properties": {"citations": {"description": "Output only. List of citations.", "items": {"$ref": "GoogleCloudAiplatformV1beta1Citation"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1CodeExecutionResult": {"description": "Result of executing the [ExecutableCode]. Only generated when using the [CodeExecution] tool, and always follows a `part` containing the [ExecutableCode].", "id": "GoogleCloudAiplatformV1beta1CodeExecutionResult", "properties": {"outcome": {"description": "Required. Outcome of the code execution.", "enum": ["OUTCOME_UNSPECIFIED", "OUTCOME_OK", "OUTCOME_FAILED", "OUTCOME_DEADLINE_EXCEEDED"], "enumDescriptions": ["Unspecified status. This value should not be used.", "Code execution completed successfully.", "Code execution finished but with a failure. `stderr` should contain the reason.", "Code execution ran for too long, and was cancelled. There may or may not be a partial output present."], "type": "string"}, "output": {"description": "Optional. Contains stdout when code execution is successful, stderr or other description otherwise.", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1Content": {"description": "The base structured datatype containing multi-part content of a message. A `Content` includes a `role` field designating the producer of the `Content` and a `parts` field containing multi-part data that contains the content of the message turn.", "id": "GoogleCloudAiplatformV1beta1Content", "properties": {"parts": {"description": "Required. Ordered `Parts` that constitute a single message. Parts may have different IANA MIME types.", "items": {"$ref": "GoogleCloudAiplatformV1beta1Part"}, "type": "array"}, "role": {"description": "Optional. The producer of the content. Must be either 'user' or 'model'. Useful to set for multi-turn conversations, otherwise can be left blank or unset.", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1CountTokensRequest": {"description": "Request message for PredictionService.CountTokens.", "id": "GoogleCloudAiplatformV1beta1CountTokensRequest", "properties": {"contents": {"description": "Optional. Input content.", "items": {"$ref": "GoogleCloudAiplatformV1beta1Content"}, "type": "array"}, "generationConfig": {"$ref": "GoogleCloudAiplatformV1beta1GenerationConfig", "description": "Optional. Generation config that the model will use to generate the response."}, "instances": {"description": "Optional. The instances that are the input to token counting call. Schema is identical to the prediction schema of the underlying model.", "items": {"type": "any"}, "type": "array"}, "model": {"description": "Optional. The name of the publisher model requested to serve the prediction. Format: `projects/{project}/locations/{location}/publishers/*/models/*`", "type": "string"}, "systemInstruction": {"$ref": "GoogleCloudAiplatformV1beta1Content", "description": "Optional. The user provided system instructions for the model. Note: only text should be used in parts and content in each part will be in a separate paragraph."}, "tools": {"description": "Optional. A list of `Tools` the model may use to generate the next response. A `Tool` is a piece of code that enables the system to interact with external systems to perform an action, or set of actions, outside of knowledge and scope of the model.", "items": {"$ref": "GoogleCloudAiplatformV1beta1Tool"}, "type": "array"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1CountTokensResponse": {"description": "Response message for PredictionService.CountTokens.", "id": "GoogleCloudAiplatformV1beta1CountTokensResponse", "properties": {"promptTokensDetails": {"description": "Output only. List of modalities that were processed in the request input.", "items": {"$ref": "GoogleCloudAiplatformV1beta1ModalityTokenCount"}, "readOnly": true, "type": "array"}, "totalBillableCharacters": {"description": "The total number of billable characters counted across all instances from the request.", "format": "int32", "type": "integer"}, "totalTokens": {"description": "The total number of tokens counted across all instances from the request.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1DynamicRetrievalConfig": {"description": "Describes the options to customize dynamic retrieval.", "id": "GoogleCloudAiplatformV1beta1DynamicRetrievalConfig", "properties": {"dynamicThreshold": {"description": "Optional. The threshold to be used in dynamic retrieval. If not set, a system default value is used.", "format": "float", "type": "number"}, "mode": {"description": "The mode of the predictor to be used in dynamic retrieval.", "enum": ["MODE_UNSPECIFIED", "MODE_DYNAMIC"], "enumDescriptions": ["Always trigger retrieval.", "Run retrieval only when system decides it is necessary."], "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1EnterpriseWebSearch": {"description": "Tool to search public web data, powered by Vertex AI Search and Sec4 compliance.", "id": "GoogleCloudAiplatformV1beta1EnterpriseWebSearch", "properties": {}, "type": "object"}, "GoogleCloudAiplatformV1beta1ExecutableCode": {"description": "Code generated by the model that is meant to be executed, and the result returned to the model. Generated when using the [CodeExecution] tool, in which the code will be automatically executed, and a corresponding [CodeExecutionResult] will also be generated.", "id": "GoogleCloudAiplatformV1beta1ExecutableCode", "properties": {"code": {"description": "Required. The code to be executed.", "type": "string"}, "language": {"description": "Required. Programming language of the `code`.", "enum": ["LANGUAGE_UNSPECIFIED", "PYTHON"], "enumDescriptions": ["Unspecified language. This value should not be used.", "Python >= 3.10, with numpy and simpy available."], "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1ExternalApi": {"description": "Retrieve from data source powered by external API for grounding. The external API is not owned by Google, but need to follow the pre-defined API spec.", "id": "GoogleCloudAiplatformV1beta1ExternalApi", "properties": {"apiAuth": {"$ref": "GoogleCloudAiplatformV1beta1ApiAuth", "deprecated": true, "description": "The authentication config to access the API. Deprecated. Please use auth_config instead."}, "apiSpec": {"description": "The API spec that the external API implements.", "enum": ["API_SPEC_UNSPECIFIED", "SIMPLE_SEARCH", "ELASTIC_SEARCH"], "enumDescriptions": ["Unspecified API spec. This value should not be used.", "Simple search API spec.", "Elastic search API spec."], "type": "string"}, "authConfig": {"$ref": "GoogleCloudAiplatformV1beta1AuthConfig", "description": "The authentication config to access the API."}, "elasticSearchParams": {"$ref": "GoogleCloudAiplatformV1beta1ExternalApiElasticSearchParams", "description": "Parameters for the elastic search API."}, "endpoint": {"description": "The endpoint of the external API. The system will call the API at this endpoint to retrieve the data for grounding. Example: https://acme.com:443/search", "type": "string"}, "simpleSearchParams": {"$ref": "GoogleCloudAiplatformV1beta1ExternalApiSimpleSearchParams", "description": "Parameters for the simple search API."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1ExternalApiElasticSearchParams": {"description": "The search parameters to use for the ELASTIC_SEARCH spec.", "id": "GoogleCloudAiplatformV1beta1ExternalApiElasticSearchParams", "properties": {"index": {"description": "The ElasticSearch index to use.", "type": "string"}, "numHits": {"description": "Optional. Number of hits (chunks) to request. When specified, it is passed to Elasticsearch as the `num_hits` param.", "format": "int32", "type": "integer"}, "searchTemplate": {"description": "The ElasticSearch search template to use.", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1ExternalApiSimpleSearchParams": {"description": "The search parameters to use for SIMPLE_SEARCH spec.", "id": "GoogleCloudAiplatformV1beta1ExternalApiSimpleSearchParams", "properties": {}, "type": "object"}, "GoogleCloudAiplatformV1beta1FileData": {"description": "URI based data.", "id": "GoogleCloudAiplatformV1beta1FileData", "properties": {"displayName": {"description": "Optional. Display name of the file data. Used to provide a label or filename to distinguish file datas. This field is only returned in PromptMessage for prompt management. It is currently used in the Gemini GenerateContent calls only when server side tools (code_execution, google_search, and url_context) are enabled.", "type": "string"}, "fileUri": {"description": "Required. URI.", "type": "string"}, "mimeType": {"description": "Required. The IANA standard MIME type of the source data.", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1FunctionCall": {"description": "A predicted [FunctionCall] returned from the model that contains a string representing the [FunctionDeclaration.name] and a structured JSON object containing the parameters and their values.", "id": "GoogleCloudAiplatformV1beta1FunctionCall", "properties": {"args": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Optional. The function parameters and values in JSON object format. See [FunctionDeclaration.parameters] for parameter details.", "type": "object"}, "id": {"description": "Optional. The unique id of the function call. If populated, the client to execute the `function_call` and return the response with the matching `id`.", "type": "string"}, "name": {"description": "Required. The name of the function to call. Matches [FunctionDeclaration.name].", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1FunctionCallingConfig": {"description": "Function calling config.", "id": "GoogleCloudAiplatformV1beta1FunctionCallingConfig", "properties": {"allowedFunctionNames": {"description": "Optional. Function names to call. Only set when the Mode is ANY. Function names should match [FunctionDeclaration.name]. With mode set to ANY, model will predict a function call from the set of function names provided.", "items": {"type": "string"}, "type": "array"}, "mode": {"description": "Optional. Function calling mode.", "enum": ["MODE_UNSPECIFIED", "AUTO", "ANY", "NONE"], "enumDescriptions": ["Unspecified function calling mode. This value should not be used.", "Default model behavior, model decides to predict either function calls or natural language response.", "Model is constrained to always predicting function calls only. If \"allowed_function_names\" are set, the predicted function calls will be limited to any one of \"allowed_function_names\", else the predicted function calls will be any one of the provided \"function_declarations\".", "Model will not predict any function calls. Model behavior is same as when not passing any function declarations."], "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1FunctionDeclaration": {"description": "Structured representation of a function declaration as defined by the [OpenAPI 3.0 specification](https://spec.openapis.org/oas/v3.0.3). Included in this declaration are the function name, description, parameters and response type. This FunctionDeclaration is a representation of a block of code that can be used as a `Tool` by the model and executed by the client.", "id": "GoogleCloudAiplatformV1beta1FunctionDeclaration", "properties": {"description": {"description": "Optional. Description and purpose of the function. Model uses it to decide how and whether to call the function.", "type": "string"}, "name": {"description": "Required. The name of the function to call. Must start with a letter or an underscore. Must be a-z, A-Z, 0-9, or contain underscores, dots and dashes, with a maximum length of 64.", "type": "string"}, "parameters": {"$ref": "GoogleCloudAiplatformV1beta1Schema", "description": "Optional. Describes the parameters to this function in JSON Schema Object format. Reflects the Open API 3.03 Parameter Object. string Key: the name of the parameter. Parameter names are case sensitive. Schema Value: the Schema defining the type used for the parameter. For function with no parameters, this can be left unset. Parameter names must start with a letter or an underscore and must only contain chars a-z, A-Z, 0-9, or underscores with a maximum length of 64. Example with 1 required and 1 optional parameter: type: OBJECT properties: param1: type: STRING param2: type: INTEGER required: - param1"}, "parametersJsonSchema": {"description": "Optional. Describes the parameters to the function in JSON Schema format. The schema must describe an object where the properties are the parameters to the function. For example: ``` { \"type\": \"object\", \"properties\": { \"name\": { \"type\": \"string\" }, \"age\": { \"type\": \"integer\" } }, \"additionalProperties\": false, \"required\": [\"name\", \"age\"], \"propertyOrdering\": [\"name\", \"age\"] } ``` This field is mutually exclusive with `parameters`.", "type": "any"}, "response": {"$ref": "GoogleCloudAiplatformV1beta1Schema", "description": "Optional. Describes the output from this function in JSON Schema format. Reflects the Open API 3.03 Response Object. The Schema defines the type used for the response value of the function."}, "responseJsonSchema": {"description": "Optional. Describes the output from this function in JSON Schema format. The value specified by the schema is the response value of the function. This field is mutually exclusive with `response`.", "type": "any"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1FunctionResponse": {"description": "The result output from a [FunctionCall] that contains a string representing the [FunctionDeclaration.name] and a structured JSON object containing any output from the function is used as context to the model. This should contain the result of a [FunctionCall] made based on model prediction.", "id": "GoogleCloudAiplatformV1beta1FunctionResponse", "properties": {"id": {"description": "Optional. The id of the function call this response is for. Populated by the client to match the corresponding function call `id`.", "type": "string"}, "name": {"description": "Required. The name of the function to call. Matches [FunctionDeclaration.name] and [FunctionCall.name].", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Required. The function response in JSON object format. Use \"output\" key to specify function output and \"error\" key to specify error details (if any). If \"output\" and \"error\" keys are not specified, then whole \"response\" is treated as function output.", "type": "object"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GenerateContentRequest": {"description": "Request message for [PredictionService.GenerateContent].", "id": "GoogleCloudAiplatformV1beta1GenerateContentRequest", "properties": {"cachedContent": {"description": "Optional. The name of the cached content used as context to serve the prediction. Note: only used in explicit caching, where users can have control over caching (e.g. what content to cache) and enjoy guaranteed cost savings. Format: `projects/{project}/locations/{location}/cachedContents/{cachedContent}`", "type": "string"}, "contents": {"description": "Required. The content of the current conversation with the model. For single-turn queries, this is a single instance. For multi-turn queries, this is a repeated field that contains conversation history + latest request.", "items": {"$ref": "GoogleCloudAiplatformV1beta1Content"}, "type": "array"}, "generationConfig": {"$ref": "GoogleCloudAiplatformV1beta1GenerationConfig", "description": "Optional. Generation config."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels with user-defined metadata for the request. It is used for billing and reporting only. Label keys and values can be no longer than 63 characters (Unicode codepoints) and can only contain lowercase letters, numeric characters, underscores, and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter.", "type": "object"}, "safetySettings": {"description": "Optional. Per request settings for blocking unsafe content. Enforced on GenerateContentResponse.candidates.", "items": {"$ref": "GoogleCloudAiplatformV1beta1SafetySetting"}, "type": "array"}, "systemInstruction": {"$ref": "GoogleCloudAiplatformV1beta1Content", "description": "Optional. The user provided system instructions for the model. Note: only text should be used in parts and content in each part will be in a separate paragraph."}, "toolConfig": {"$ref": "GoogleCloudAiplatformV1beta1ToolConfig", "description": "Optional. Tool config. This config is shared for all tools provided in the request."}, "tools": {"description": "Optional. A list of `Tools` the model may use to generate the next response. A `Tool` is a piece of code that enables the system to interact with external systems to perform an action, or set of actions, outside of knowledge and scope of the model.", "items": {"$ref": "GoogleCloudAiplatformV1beta1Tool"}, "type": "array"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GenerateContentResponse": {"description": "Response message for [PredictionService.GenerateContent].", "id": "GoogleCloudAiplatformV1beta1GenerateContentResponse", "properties": {"candidates": {"description": "Output only. Generated candidates.", "items": {"$ref": "GoogleCloudAiplatformV1beta1Candidate"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. Timestamp when the request is made to the server.", "format": "google-datetime", "readOnly": true, "type": "string"}, "modelVersion": {"description": "Output only. The model version used to generate the response.", "readOnly": true, "type": "string"}, "promptFeedback": {"$ref": "GoogleCloudAiplatformV1beta1GenerateContentResponsePromptFeedback", "description": "Output only. Content filter results for a prompt sent in the request. Note: <PERSON><PERSON> only in the first stream chunk. Only happens when no candidates were generated due to content violations.", "readOnly": true}, "responseId": {"description": "Output only. response_id is used to identify each response. It is the encoding of the event_id.", "readOnly": true, "type": "string"}, "usageMetadata": {"$ref": "GoogleCloudAiplatformV1beta1GenerateContentResponseUsageMetadata", "description": "Usage metadata about the response(s)."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GenerateContentResponsePromptFeedback": {"description": "Content filter results for a prompt sent in the request.", "id": "GoogleCloudAiplatformV1beta1GenerateContentResponsePromptFeedback", "properties": {"blockReason": {"description": "Output only. Blocked reason.", "enum": ["BLOCKED_REASON_UNSPECIFIED", "SAFETY", "OTHER", "BLOCKLIST", "PROHIBITED_CONTENT", "IMAGE_SAFETY"], "enumDescriptions": ["Unspecified blocked reason.", "Candidates blocked due to safety.", "Candidates blocked due to other reason.", "Candidates blocked due to the terms which are included from the terminology blocklist.", "Candidates blocked due to prohibited content.", "Candidates blocked due to unsafe image generation content."], "readOnly": true, "type": "string"}, "blockReasonMessage": {"description": "Output only. A readable block reason message.", "readOnly": true, "type": "string"}, "safetyRatings": {"description": "Output only. Safety ratings.", "items": {"$ref": "GoogleCloudAiplatformV1beta1SafetyRating"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GenerateContentResponseUsageMetadata": {"description": "Usage metadata about response(s).", "id": "GoogleCloudAiplatformV1beta1GenerateContentResponseUsageMetadata", "properties": {"cacheTokensDetails": {"description": "Output only. List of modalities of the cached content in the request input.", "items": {"$ref": "GoogleCloudAiplatformV1beta1ModalityTokenCount"}, "readOnly": true, "type": "array"}, "cachedContentTokenCount": {"description": "Output only. Number of tokens in the cached part in the input (the cached content).", "format": "int32", "readOnly": true, "type": "integer"}, "candidatesTokenCount": {"description": "Number of tokens in the response(s).", "format": "int32", "type": "integer"}, "candidatesTokensDetails": {"description": "Output only. List of modalities that were returned in the response.", "items": {"$ref": "GoogleCloudAiplatformV1beta1ModalityTokenCount"}, "readOnly": true, "type": "array"}, "promptTokenCount": {"description": "Number of tokens in the request. When `cached_content` is set, this is still the total effective prompt size meaning this includes the number of tokens in the cached content.", "format": "int32", "type": "integer"}, "promptTokensDetails": {"description": "Output only. List of modalities that were processed in the request input.", "items": {"$ref": "GoogleCloudAiplatformV1beta1ModalityTokenCount"}, "readOnly": true, "type": "array"}, "thoughtsTokenCount": {"description": "Output only. Number of tokens present in thoughts output.", "format": "int32", "readOnly": true, "type": "integer"}, "toolUsePromptTokenCount": {"description": "Output only. Number of tokens present in tool-use prompt(s).", "format": "int32", "readOnly": true, "type": "integer"}, "toolUsePromptTokensDetails": {"description": "Output only. List of modalities that were processed for tool-use request inputs.", "items": {"$ref": "GoogleCloudAiplatformV1beta1ModalityTokenCount"}, "readOnly": true, "type": "array"}, "totalTokenCount": {"description": "Total token count for prompt, response candidates, and tool-use prompts (if present).", "format": "int32", "type": "integer"}, "trafficType": {"description": "Output only. Traffic type. This shows whether a request consumes Pay-As-You-Go or Provisioned Throughput quota.", "enum": ["TRAFFIC_TYPE_UNSPECIFIED", "ON_DEMAND", "PROVISIONED_THROUGHPUT"], "enumDescriptions": ["Unspecified request traffic type.", "Type for Pay-As-You-Go traffic.", "Type for Provisioned Throughput traffic."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GenerationConfig": {"description": "Generation config.", "id": "GoogleCloudAiplatformV1beta1GenerationConfig", "properties": {"audioTimestamp": {"description": "Optional. If enabled, audio timestamp will be included in the request to the model.", "type": "boolean"}, "candidateCount": {"description": "Optional. Number of candidates to generate.", "format": "int32", "type": "integer"}, "enableAffectiveDialog": {"description": "Optional. If enabled, the model will detect emotions and adapt its responses accordingly.", "type": "boolean"}, "frequencyPenalty": {"description": "Optional. Frequency penalties.", "format": "float", "type": "number"}, "logprobs": {"description": "Optional. Logit probabilities.", "format": "int32", "type": "integer"}, "maxOutputTokens": {"description": "Optional. The maximum number of output tokens to generate per message.", "format": "int32", "type": "integer"}, "mediaResolution": {"description": "Optional. If specified, the media resolution specified will be used.", "enum": ["MEDIA_RESOLUTION_UNSPECIFIED", "MEDIA_RESOLUTION_LOW", "MEDIA_RESOLUTION_MEDIUM", "MEDIA_RESOLUTION_HIGH"], "enumDescriptions": ["Media resolution has not been set.", "Media resolution set to low (64 tokens).", "Media resolution set to medium (256 tokens).", "Media resolution set to high (zoomed reframing with 256 tokens)."], "type": "string"}, "modelConfig": {"$ref": "GoogleCloudAiplatformV1beta1GenerationConfigModelConfig", "description": "Optional. Config for model selection."}, "presencePenalty": {"description": "Optional. Positive penalties.", "format": "float", "type": "number"}, "responseJsonSchema": {"description": "Optional. Output schema of the generated response. This is an alternative to `response_schema` that accepts [JSON Schema](https://json-schema.org/). If set, `response_schema` must be omitted, but `response_mime_type` is required. While the full JSON Schema may be sent, not all features are supported. Specifically, only the following properties are supported: - `$id` - `$defs` - `$ref` - `$anchor` - `type` - `format` - `title` - `description` - `enum` (for strings and numbers) - `items` - `prefixItems` - `minItems` - `maxItems` - `minimum` - `maximum` - `anyOf` - `oneOf` (interpreted the same as `anyOf`) - `properties` - `additionalProperties` - `required` The non-standard `propertyOrdering` property may also be set. Cyclic references are unrolled to a limited degree and, as such, may only be used within non-required properties. (Nullable properties are not sufficient.) If `$ref` is set on a sub-schema, no other properties, except for than those starting as a `$`, may be set.", "type": "any"}, "responseLogprobs": {"description": "Optional. If true, export the logprobs results in response.", "type": "boolean"}, "responseMimeType": {"description": "Optional. Output response mimetype of the generated candidate text. Supported mimetype: - `text/plain`: (default) Text output. - `application/json`: JSON response in the candidates. The model needs to be prompted to output the appropriate response type, otherwise the behavior is undefined. This is a preview feature.", "type": "string"}, "responseModalities": {"description": "Optional. The modalities of the response.", "items": {"enum": ["MODALITY_UNSPECIFIED", "TEXT", "IMAGE", "AUDIO"], "enumDescriptions": ["Unspecified modality. Will be processed as text.", "Text modality.", "Image modality.", "Audio modality."], "type": "string"}, "type": "array"}, "responseSchema": {"$ref": "GoogleCloudAiplatformV1beta1Schema", "description": "Optional. The `Schema` object allows the definition of input and output data types. These types can be objects, but also primitives and arrays. Represents a select subset of an [OpenAPI 3.0 schema object](https://spec.openapis.org/oas/v3.0.3#schema). If set, a compatible response_mime_type must also be set. Compatible mimetypes: `application/json`: Schema for JSON response."}, "routingConfig": {"$ref": "GoogleCloudAiplatformV1beta1GenerationConfigRoutingConfig", "description": "Optional. Routing configuration."}, "seed": {"description": "Optional. Seed.", "format": "int32", "type": "integer"}, "speechConfig": {"$ref": "GoogleCloudAiplatformV1beta1SpeechConfig", "description": "Optional. The speech generation config."}, "stopSequences": {"description": "Optional. Stop sequences.", "items": {"type": "string"}, "type": "array"}, "temperature": {"description": "Optional. Controls the randomness of predictions.", "format": "float", "type": "number"}, "thinkingConfig": {"$ref": "GoogleCloudAiplatformV1beta1GenerationConfigThinkingConfig", "description": "Optional. Config for thinking features. An error will be returned if this field is set for models that don't support thinking."}, "topK": {"description": "Optional. If specified, top-k sampling will be used.", "format": "float", "type": "number"}, "topP": {"description": "Optional. If specified, nucleus sampling will be used.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GenerationConfigModelConfig": {"description": "Config for model selection.", "id": "GoogleCloudAiplatformV1beta1GenerationConfigModelConfig", "properties": {"featureSelectionPreference": {"description": "Required. Feature selection preference.", "enum": ["FEATURE_SELECTION_PREFERENCE_UNSPECIFIED", "PRIORITIZE_QUALITY", "BALANCED", "PRIORITIZE_COST"], "enumDescriptions": ["Unspecified feature selection preference.", "Prefer higher quality over lower cost.", "Balanced feature selection preference.", "Prefer lower cost over higher quality."], "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GenerationConfigRoutingConfig": {"description": "The configuration for routing the request to a specific model.", "id": "GoogleCloudAiplatformV1beta1GenerationConfigRoutingConfig", "properties": {"autoMode": {"$ref": "GoogleCloudAiplatformV1beta1GenerationConfigRoutingConfigAutoRoutingMode", "description": "Automated routing."}, "manualMode": {"$ref": "GoogleCloudAiplatformV1beta1GenerationConfigRoutingConfigManualRoutingMode", "description": "Manual routing."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GenerationConfigRoutingConfigAutoRoutingMode": {"description": "When automated routing is specified, the routing will be determined by the pretrained routing model and customer provided model routing preference.", "id": "GoogleCloudAiplatformV1beta1GenerationConfigRoutingConfigAutoRoutingMode", "properties": {"modelRoutingPreference": {"description": "The model routing preference.", "enum": ["UNKNOWN", "PRIORITIZE_QUALITY", "BALANCED", "PRIORITIZE_COST"], "enumDescriptions": ["Unspecified model routing preference.", "Prefer higher quality over low cost.", "Balanced model routing preference.", "Prefer lower cost over higher quality."], "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GenerationConfigRoutingConfigManualRoutingMode": {"description": "When manual routing is set, the specified model will be used directly.", "id": "GoogleCloudAiplatformV1beta1GenerationConfigRoutingConfigManualRoutingMode", "properties": {"modelName": {"description": "The model name to use. Only the public LLM models are accepted. See [Supported models](https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/inference#supported-models).", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GenerationConfigThinkingConfig": {"description": "Config for thinking features.", "id": "GoogleCloudAiplatformV1beta1GenerationConfigThinkingConfig", "properties": {"includeThoughts": {"description": "Optional. Indicates whether to include thoughts in the response. If true, thoughts are returned only when available.", "type": "boolean"}, "thinkingBudget": {"description": "Optional. Indicates the thinking budget in tokens.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GoogleSearchRetrieval": {"description": "Tool to retrieve public web data for grounding, powered by Google.", "id": "GoogleCloudAiplatformV1beta1GoogleSearchRetrieval", "properties": {"dynamicRetrievalConfig": {"$ref": "GoogleCloudAiplatformV1beta1DynamicRetrievalConfig", "description": "Specifies the dynamic retrieval configuration for the given source."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GroundingChunk": {"description": "Grounding chunk.", "id": "GoogleCloudAiplatformV1beta1GroundingChunk", "properties": {"retrievedContext": {"$ref": "GoogleCloudAiplatformV1beta1GroundingChunkRetrievedContext", "description": "Grounding chunk from context retrieved by the retrieval tools."}, "web": {"$ref": "GoogleCloudAiplatformV1beta1GroundingChunkWeb", "description": "Grounding chunk from the web."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GroundingChunkRetrievedContext": {"description": "Chunk from context retrieved by the retrieval tools.", "id": "GoogleCloudAiplatformV1beta1GroundingChunkRetrievedContext", "properties": {"ragChunk": {"$ref": "GoogleCloudAiplatformV1beta1RagChunk", "description": "Additional context for the RAG retrieval result. This is only populated when using the RAG retrieval tool."}, "text": {"description": "Text of the attribution.", "type": "string"}, "title": {"description": "Title of the attribution.", "type": "string"}, "uri": {"description": "URI reference of the attribution.", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GroundingChunkWeb": {"description": "Chunk from the web.", "id": "GoogleCloudAiplatformV1beta1GroundingChunkWeb", "properties": {"domain": {"description": "Domain of the (original) URI.", "type": "string"}, "title": {"description": "Title of the chunk.", "type": "string"}, "uri": {"description": "URI reference of the chunk.", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GroundingMetadata": {"description": "Metadata returned to client when grounding is enabled.", "id": "GoogleCloudAiplatformV1beta1GroundingMetadata", "properties": {"groundingChunks": {"description": "List of supporting references retrieved from specified grounding source.", "items": {"$ref": "GoogleCloudAiplatformV1beta1GroundingChunk"}, "type": "array"}, "groundingSupports": {"description": "Optional. List of grounding support.", "items": {"$ref": "GoogleCloudAiplatformV1beta1GroundingSupport"}, "type": "array"}, "retrievalMetadata": {"$ref": "GoogleCloudAiplatformV1beta1RetrievalMetadata", "description": "Optional. Output only. Retrieval metadata.", "readOnly": true}, "retrievalQueries": {"description": "Optional. Queries executed by the retrieval tools.", "items": {"type": "string"}, "type": "array"}, "searchEntryPoint": {"$ref": "GoogleCloudAiplatformV1beta1SearchEntryPoint", "description": "Optional. Google search entry for the following-up web searches."}, "webSearchQueries": {"description": "Optional. Web search queries for the following-up web search.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1GroundingSupport": {"description": "Grounding support.", "id": "GoogleCloudAiplatformV1beta1GroundingSupport", "properties": {"confidenceScores": {"description": "Confidence score of the support references. Ranges from 0 to 1. 1 is the most confident. For Gemini 2.0 and before, this list must have the same size as the grounding_chunk_indices. For Gemini 2.5 and after, this list will be empty and should be ignored.", "items": {"format": "float", "type": "number"}, "type": "array"}, "groundingChunkIndices": {"description": "A list of indices (into 'grounding_chunk') specifying the citations associated with the claim. For instance [1,3,4] means that grounding_chunk[1], grounding_chunk[3], grounding_chunk[4] are the retrieved content attributed to the claim.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "segment": {"$ref": "GoogleCloudAiplatformV1beta1Segment", "description": "Segment of the content this support belongs to."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1LogprobsResult": {"description": "Logprobs Result", "id": "GoogleCloudAiplatformV1beta1LogprobsResult", "properties": {"chosenCandidates": {"description": "Length = total number of decoding steps. The chosen candidates may or may not be in top_candidates.", "items": {"$ref": "GoogleCloudAiplatformV1beta1LogprobsResultCandidate"}, "type": "array"}, "topCandidates": {"description": "Length = total number of decoding steps.", "items": {"$ref": "GoogleCloudAiplatformV1beta1LogprobsResultTopCandidates"}, "type": "array"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1LogprobsResultCandidate": {"description": "Candidate for the logprobs token and score.", "id": "GoogleCloudAiplatformV1beta1LogprobsResultCandidate", "properties": {"logProbability": {"description": "The candidate's log probability.", "format": "float", "type": "number"}, "token": {"description": "The candidate's token string value.", "type": "string"}, "tokenId": {"description": "The candidate's token id value.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1LogprobsResultTopCandidates": {"description": "Candidates with top log probabilities at each decoding step.", "id": "GoogleCloudAiplatformV1beta1LogprobsResultTopCandidates", "properties": {"candidates": {"description": "Sorted by log probability in descending order.", "items": {"$ref": "GoogleCloudAiplatformV1beta1LogprobsResultCandidate"}, "type": "array"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1ModalityTokenCount": {"description": "Represents token counting info for a single modality.", "id": "GoogleCloudAiplatformV1beta1ModalityTokenCount", "properties": {"modality": {"description": "The modality associated with this token count.", "enum": ["MODALITY_UNSPECIFIED", "TEXT", "IMAGE", "VIDEO", "AUDIO", "DOCUMENT"], "enumDescriptions": ["Unspecified modality.", "Plain text.", "Image.", "Video.", "Audio.", "Document, e.g. PDF."], "type": "string"}, "tokenCount": {"description": "Number of tokens.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1Part": {"description": "A datatype containing media that is part of a multi-part `Content` message. A `Part` consists of data which has an associated datatype. A `Part` can only contain one of the accepted types in `Part.data`. A `Part` must have a fixed IANA MIME type identifying the type and subtype of the media if `inline_data` or `file_data` field is filled with raw bytes.", "id": "GoogleCloudAiplatformV1beta1Part", "properties": {"codeExecutionResult": {"$ref": "GoogleCloudAiplatformV1beta1CodeExecutionResult", "description": "Optional. Result of executing the [ExecutableCode]."}, "executableCode": {"$ref": "GoogleCloudAiplatformV1beta1ExecutableCode", "description": "Optional. Code generated by the model that is meant to be executed."}, "fileData": {"$ref": "GoogleCloudAiplatformV1beta1FileData", "description": "Optional. URI based data."}, "functionCall": {"$ref": "GoogleCloudAiplatformV1beta1FunctionCall", "description": "Optional. A predicted [FunctionCall] returned from the model that contains a string representing the [FunctionDeclaration.name] with the parameters and their values."}, "functionResponse": {"$ref": "GoogleCloudAiplatformV1beta1FunctionResponse", "description": "Optional. The result output of a [FunctionCall] that contains a string representing the [FunctionDeclaration.name] and a structured JSON object containing any output from the function call. It is used as context to the model."}, "inlineData": {"$ref": "GoogleCloudAiplatformV1beta1Blob", "description": "Optional. Inlined bytes data."}, "text": {"description": "Optional. Text part (can be code).", "type": "string"}, "thought": {"description": "Optional. Indicates if the part is thought from the model.", "type": "boolean"}, "thoughtSignature": {"description": "Optional. An opaque signature for the thought so it can be reused in subsequent requests.", "format": "byte", "type": "string"}, "videoMetadata": {"$ref": "GoogleCloudAiplatformV1beta1VideoMetadata", "description": "Optional. Video metadata. The metadata should only be specified while the video data is presented in inline_data or file_data."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1PrebuiltVoiceConfig": {"description": "The configuration for the prebuilt speaker to use.", "id": "GoogleCloudAiplatformV1beta1PrebuiltVoiceConfig", "properties": {"voiceName": {"description": "The name of the preset voice to use.", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1RagChunk": {"description": "A RagChunk includes the content of a chunk of a RagFile, and associated metadata.", "id": "GoogleCloudAiplatformV1beta1RagChunk", "properties": {"pageSpan": {"$ref": "GoogleCloudAiplatformV1beta1RagChunkPageSpan", "description": "If populated, represents where the chunk starts and ends in the document."}, "text": {"description": "The content of the chunk.", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1RagChunkPageSpan": {"description": "Represents where the chunk starts and ends in the document.", "id": "GoogleCloudAiplatformV1beta1RagChunkPageSpan", "properties": {"firstPage": {"description": "Page where chunk starts in the document. Inclusive. 1-indexed.", "format": "int32", "type": "integer"}, "lastPage": {"description": "Page where chunk ends in the document. Inclusive. 1-indexed.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1RagRetrievalConfig": {"description": "Specifies the context retrieval config.", "id": "GoogleCloudAiplatformV1beta1RagRetrievalConfig", "properties": {"filter": {"$ref": "GoogleCloudAiplatformV1beta1RagRetrievalConfigFilter", "description": "Optional. Config for filters."}, "hybridSearch": {"$ref": "GoogleCloudAiplatformV1beta1RagRetrievalConfigHybridSearch", "description": "Optional. Config for Hybrid Search."}, "ranking": {"$ref": "GoogleCloudAiplatformV1beta1RagRetrievalConfigRanking", "description": "Optional. Config for ranking and reranking."}, "topK": {"description": "Optional. The number of contexts to retrieve.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1RagRetrievalConfigFilter": {"description": "Config for filters.", "id": "GoogleCloudAiplatformV1beta1RagRetrievalConfigFilter", "properties": {"metadataFilter": {"description": "Optional. String for metadata filtering.", "type": "string"}, "vectorDistanceThreshold": {"description": "Optional. Only returns contexts with vector distance smaller than the threshold.", "format": "double", "type": "number"}, "vectorSimilarityThreshold": {"description": "Optional. Only returns contexts with vector similarity larger than the threshold.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1RagRetrievalConfigHybridSearch": {"description": "Config for Hybrid Search.", "id": "GoogleCloudAiplatformV1beta1RagRetrievalConfigHybridSearch", "properties": {"alpha": {"description": "Optional. Alpha value controls the weight between dense and sparse vector search results. The range is [0, 1], while 0 means sparse vector search only and 1 means dense vector search only. The default value is 0.5 which balances sparse and dense vector search equally.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1RagRetrievalConfigRanking": {"description": "Config for ranking and reranking.", "id": "GoogleCloudAiplatformV1beta1RagRetrievalConfigRanking", "properties": {"llmRanker": {"$ref": "GoogleCloudAiplatformV1beta1RagRetrievalConfigRankingLlmRanker", "description": "Optional. Config for LlmRanker."}, "rankService": {"$ref": "GoogleCloudAiplatformV1beta1RagRetrievalConfigRankingRankService", "description": "Optional. Config for Rank Service."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1RagRetrievalConfigRankingLlmRanker": {"description": "Config for LlmRanker.", "id": "GoogleCloudAiplatformV1beta1RagRetrievalConfigRankingLlmRanker", "properties": {"modelName": {"description": "Optional. The model name used for ranking. See [Supported models](https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/inference#supported-models).", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1RagRetrievalConfigRankingRankService": {"description": "Config for Rank Service.", "id": "GoogleCloudAiplatformV1beta1RagRetrievalConfigRankingRankService", "properties": {"modelName": {"description": "Optional. The model name of the rank service. Format: `semantic-ranker-512@latest`", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1Retrieval": {"description": "Defines a retrieval tool that model can call to access external knowledge.", "id": "GoogleCloudAiplatformV1beta1Retrieval", "properties": {"disableAttribution": {"deprecated": true, "description": "Optional. Deprecated. This option is no longer supported.", "type": "boolean"}, "externalApi": {"$ref": "GoogleCloudAiplatformV1beta1ExternalApi", "description": "Use data source powered by external API for grounding."}, "vertexAiSearch": {"$ref": "GoogleCloudAiplatformV1beta1VertexAISearch", "description": "Set to use data source powered by Vertex AI Search."}, "vertexRagStore": {"$ref": "GoogleCloudAiplatformV1beta1VertexRagStore", "description": "Set to use data source powered by Vertex RAG store. User data is uploaded via the VertexRagDataService."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1RetrievalConfig": {"description": "Retrieval config.", "id": "GoogleCloudAiplatformV1beta1RetrievalConfig", "properties": {"languageCode": {"description": "The language code of the user.", "type": "string"}, "latLng": {"$ref": "LatLng", "description": "The location of the user."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1RetrievalMetadata": {"description": "Metadata related to retrieval in the grounding flow.", "id": "GoogleCloudAiplatformV1beta1RetrievalMetadata", "properties": {"googleSearchDynamicRetrievalScore": {"description": "Optional. Score indicating how likely information from Google Search could help answer the prompt. The score is in the range `[0, 1]`, where 0 is the least likely and 1 is the most likely. This score is only populated when Google Search grounding and dynamic retrieval is enabled. It will be compared to the threshold to determine whether to trigger Google Search.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1SafetyRating": {"description": "Safety rating corresponding to the generated content.", "id": "GoogleCloudAiplatformV1beta1SafetyRating", "properties": {"blocked": {"description": "Output only. Indicates whether the content was filtered out because of this rating.", "readOnly": true, "type": "boolean"}, "category": {"description": "Output only. Harm category.", "enum": ["HARM_CATEGORY_UNSPECIFIED", "HARM_CATEGORY_HATE_SPEECH", "HARM_CATEGORY_DANGEROUS_CONTENT", "HARM_CATEGORY_HARASSMENT", "HARM_CATEGORY_SEXUALLY_EXPLICIT", "HARM_CATEGORY_CIVIC_INTEGRITY", "HARM_CATEGORY_IMAGE_HATE", "HARM_CATEGORY_IMAGE_DANGEROUS_CONTENT", "HARM_CATEGORY_IMAGE_HARASSMENT", "HARM_CATEGORY_IMAGE_SEXUALLY_EXPLICIT"], "enumDeprecated": [false, false, false, false, false, true, false, false, false, false], "enumDescriptions": ["The harm category is unspecified.", "The harm category is hate speech.", "The harm category is dangerous content.", "The harm category is harassment.", "The harm category is sexually explicit content.", "Deprecated: Election filter is not longer supported. The harm category is civic integrity.", "The harm category is image hate.", "The harm category is image dangerous content.", "The harm category is image harassment.", "The harm category is image sexually explicit content."], "readOnly": true, "type": "string"}, "overwrittenThreshold": {"description": "Output only. The overwritten threshold for the safety category of Gemini 2.0 image out. If minors are detected in the output image, the threshold of each safety category will be overwritten if user sets a lower threshold.", "enum": ["HARM_BLOCK_THRESHOLD_UNSPECIFIED", "BLOCK_LOW_AND_ABOVE", "BLOCK_MEDIUM_AND_ABOVE", "BLOCK_ONLY_HIGH", "BLOCK_NONE", "OFF"], "enumDescriptions": ["Unspecified harm block threshold.", "Block low threshold and above (i.e. block more).", "Block medium threshold and above.", "Block only high threshold (i.e. block less).", "Block none.", "Turn off the safety filter."], "readOnly": true, "type": "string"}, "probability": {"description": "Output only. Harm probability levels in the content.", "enum": ["HARM_PROBABILITY_UNSPECIFIED", "NEGLIGIBLE", "LOW", "MEDIUM", "HIGH"], "enumDescriptions": ["Harm probability unspecified.", "Negligible level of harm.", "Low level of harm.", "Medium level of harm.", "High level of harm."], "readOnly": true, "type": "string"}, "probabilityScore": {"description": "Output only. Harm probability score.", "format": "float", "readOnly": true, "type": "number"}, "severity": {"description": "Output only. Harm severity levels in the content.", "enum": ["HARM_SEVERITY_UNSPECIFIED", "HARM_SEVERITY_NEGLIGIBLE", "HARM_SEVERITY_LOW", "HARM_SEVERITY_MEDIUM", "HARM_SEVERITY_HIGH"], "enumDescriptions": ["Harm severity unspecified.", "Negligible level of harm severity.", "Low level of harm severity.", "Medium level of harm severity.", "High level of harm severity."], "readOnly": true, "type": "string"}, "severityScore": {"description": "Output only. Harm severity score.", "format": "float", "readOnly": true, "type": "number"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1SafetySetting": {"description": "Safety settings.", "id": "GoogleCloudAiplatformV1beta1SafetySetting", "properties": {"category": {"description": "Required. Harm category.", "enum": ["HARM_CATEGORY_UNSPECIFIED", "HARM_CATEGORY_HATE_SPEECH", "HARM_CATEGORY_DANGEROUS_CONTENT", "HARM_CATEGORY_HARASSMENT", "HARM_CATEGORY_SEXUALLY_EXPLICIT", "HARM_CATEGORY_CIVIC_INTEGRITY", "HARM_CATEGORY_IMAGE_HATE", "HARM_CATEGORY_IMAGE_DANGEROUS_CONTENT", "HARM_CATEGORY_IMAGE_HARASSMENT", "HARM_CATEGORY_IMAGE_SEXUALLY_EXPLICIT"], "enumDeprecated": [false, false, false, false, false, true, false, false, false, false], "enumDescriptions": ["The harm category is unspecified.", "The harm category is hate speech.", "The harm category is dangerous content.", "The harm category is harassment.", "The harm category is sexually explicit content.", "Deprecated: Election filter is not longer supported. The harm category is civic integrity.", "The harm category is image hate.", "The harm category is image dangerous content.", "The harm category is image harassment.", "The harm category is image sexually explicit content."], "type": "string"}, "method": {"description": "Optional. Specify if the threshold is used for probability or severity score. If not specified, the threshold is used for probability score.", "enum": ["HARM_BLOCK_METHOD_UNSPECIFIED", "SEVERITY", "PROBABILITY"], "enumDescriptions": ["The harm block method is unspecified.", "The harm block method uses both probability and severity scores.", "The harm block method uses the probability score."], "type": "string"}, "threshold": {"description": "Required. The harm block threshold.", "enum": ["HARM_BLOCK_THRESHOLD_UNSPECIFIED", "BLOCK_LOW_AND_ABOVE", "BLOCK_MEDIUM_AND_ABOVE", "BLOCK_ONLY_HIGH", "BLOCK_NONE", "OFF"], "enumDescriptions": ["Unspecified harm block threshold.", "Block low threshold and above (i.e. block more).", "Block medium threshold and above.", "Block only high threshold (i.e. block less).", "Block none.", "Turn off the safety filter."], "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1Schema": {"description": "Schema is used to define the format of input/output data. Represents a select subset of an [OpenAPI 3.0 schema object](https://spec.openapis.org/oas/v3.0.3#schema-object). More fields may be added in the future as needed.", "id": "GoogleCloudAiplatformV1beta1Schema", "properties": {"additionalProperties": {"description": "Optional. Can either be a boolean or an object; controls the presence of additional properties.", "type": "any"}, "anyOf": {"description": "Optional. The value should be validated against any (one or more) of the subschemas in the list.", "items": {"$ref": "GoogleCloudAiplatformV1beta1Schema"}, "type": "array"}, "default": {"description": "Optional. Default value of the data.", "type": "any"}, "defs": {"additionalProperties": {"$ref": "GoogleCloudAiplatformV1beta1Schema"}, "description": "Optional. A map of definitions for use by `ref` Only allowed at the root of the schema.", "type": "object"}, "description": {"description": "Optional. The description of the data.", "type": "string"}, "enum": {"description": "Optional. Possible values of the element of primitive type with enum format. Examples: 1. We can define direction as : {type:STRING, format:enum, enum:[\"EAST\", NORTH\", \"SOUTH\", \"WEST\"]} 2. We can define apartment number as : {type:INTEGER, format:enum, enum:[\"101\", \"201\", \"301\"]}", "items": {"type": "string"}, "type": "array"}, "example": {"description": "Optional. Example of the object. Will only populated when the object is the root.", "type": "any"}, "format": {"description": "Optional. The format of the data. Supported formats: for NUMBER type: \"float\", \"double\" for INTEGER type: \"int32\", \"int64\" for STRING type: \"email\", \"byte\", etc", "type": "string"}, "items": {"$ref": "GoogleCloudAiplatformV1beta1Schema", "description": "Optional. SCHEMA FIELDS FOR TYPE ARRAY Schema of the elements of Type.ARRAY."}, "maxItems": {"description": "Optional. Maximum number of the elements for Type.ARRAY.", "format": "int64", "type": "string"}, "maxLength": {"description": "Optional. Maximum length of the Type.STRING", "format": "int64", "type": "string"}, "maxProperties": {"description": "Optional. Maximum number of the properties for Type.OBJECT.", "format": "int64", "type": "string"}, "maximum": {"description": "Optional. Maximum value of the Type.INTEGER and Type.NUMBER", "format": "double", "type": "number"}, "minItems": {"description": "Optional. Minimum number of the elements for Type.ARRAY.", "format": "int64", "type": "string"}, "minLength": {"description": "Optional. SCHEMA FIELDS FOR TYPE STRING Minimum length of the Type.STRING", "format": "int64", "type": "string"}, "minProperties": {"description": "Optional. Minimum number of the properties for Type.OBJECT.", "format": "int64", "type": "string"}, "minimum": {"description": "Optional. SCHEMA FIELDS FOR TYPE INTEGER and NUMBER Minimum value of the Type.INTEGER and Type.NUMBER", "format": "double", "type": "number"}, "nullable": {"description": "Optional. Indicates if the value may be null.", "type": "boolean"}, "pattern": {"description": "Optional. Pattern of the Type.STRING to restrict a string to a regular expression.", "type": "string"}, "properties": {"additionalProperties": {"$ref": "GoogleCloudAiplatformV1beta1Schema"}, "description": "Optional. SCHEMA FIELDS FOR TYPE OBJECT Properties of Type.OBJECT.", "type": "object"}, "propertyOrdering": {"description": "Optional. The order of the properties. Not a standard field in open api spec. Only used to support the order of the properties.", "items": {"type": "string"}, "type": "array"}, "ref": {"description": "Optional. Allows indirect references between schema nodes. The value should be a valid reference to a child of the root `defs`. For example, the following schema defines a reference to a schema node named \"Pet\": type: object properties: pet: ref: #/defs/Pet defs: Pet: type: object properties: name: type: string The value of the \"pet\" property is a reference to the schema node named \"<PERSON>\". See details in https://json-schema.org/understanding-json-schema/structuring", "type": "string"}, "required": {"description": "Optional. Required properties of Type.OBJECT.", "items": {"type": "string"}, "type": "array"}, "title": {"description": "Optional. The title of the Schema.", "type": "string"}, "type": {"description": "Optional. The type of the data.", "enum": ["TYPE_UNSPECIFIED", "STRING", "NUMBER", "INTEGER", "BOOLEAN", "ARRAY", "OBJECT", "NULL"], "enumDescriptions": ["Not specified, should not be used.", "OpenAPI string type", "OpenAPI number type", "OpenAPI integer type", "OpenAPI boolean type", "OpenAPI array type", "OpenAPI object type", "Null type"], "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1SearchEntryPoint": {"description": "Google search entry point.", "id": "GoogleCloudAiplatformV1beta1SearchEntryPoint", "properties": {"renderedContent": {"description": "Optional. Web content snippet that can be embedded in a web page or an app webview.", "type": "string"}, "sdkBlob": {"description": "Optional. Base64 encoded JSON representing array of tuple.", "format": "byte", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1Segment": {"description": "Segment of the content.", "id": "GoogleCloudAiplatformV1beta1Segment", "properties": {"endIndex": {"description": "Output only. End index in the given Part, measured in bytes. Offset from the start of the Part, exclusive, starting at zero.", "format": "int32", "readOnly": true, "type": "integer"}, "partIndex": {"description": "Output only. The index of a Part object within its parent Content object.", "format": "int32", "readOnly": true, "type": "integer"}, "startIndex": {"description": "Output only. Start index in the given Part, measured in bytes. Offset from the start of the Part, inclusive, starting at zero.", "format": "int32", "readOnly": true, "type": "integer"}, "text": {"description": "Output only. The text corresponding to the segment from the response.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1SpeechConfig": {"description": "The speech generation config.", "id": "GoogleCloudAiplatformV1beta1SpeechConfig", "properties": {"languageCode": {"description": "Optional. Language code (ISO 639. e.g. en-US) for the speech synthesization.", "type": "string"}, "voiceConfig": {"$ref": "GoogleCloudAiplatformV1beta1VoiceConfig", "description": "The configuration for the speaker to use."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1Tool": {"description": "Tool details that the model may use to generate response. A `Tool` is a piece of code that enables the system to interact with external systems to perform an action, or set of actions, outside of knowledge and scope of the model. A Tool object should contain exactly one type of Tool (e.g FunctionDeclaration, Retrieval or GoogleSearchRetrieval).", "id": "GoogleCloudAiplatformV1beta1Tool", "properties": {"codeExecution": {"$ref": "GoogleCloudAiplatformV1beta1ToolCodeExecution", "description": "Optional. CodeExecution tool type. Enables the model to execute code as part of generation."}, "computerUse": {"$ref": "GoogleCloudAiplatformV1beta1ToolComputerUse", "description": "Optional. Tool to support the model interacting directly with the computer. If enabled, it automatically populates computer-use specific Function Declarations."}, "enterpriseWebSearch": {"$ref": "GoogleCloudAiplatformV1beta1EnterpriseWebSearch", "description": "Optional. Tool to support searching public web data, powered by Vertex AI Search and Sec4 compliance."}, "functionDeclarations": {"description": "Optional. Function tool type. One or more function declarations to be passed to the model along with the current user query. Model may decide to call a subset of these functions by populating FunctionCall in the response. User should provide a FunctionResponse for each function call in the next turn. Based on the function responses, <PERSON> will generate the final response back to the user. Maximum 128 function declarations can be provided.", "items": {"$ref": "GoogleCloudAiplatformV1beta1FunctionDeclaration"}, "type": "array"}, "googleSearch": {"$ref": "GoogleCloudAiplatformV1beta1ToolGoogleSearch", "description": "Optional. GoogleSearch tool type. Tool to support Google Search in Model. Powered by Google."}, "googleSearchRetrieval": {"$ref": "GoogleCloudAiplatformV1beta1GoogleSearchRetrieval", "description": "Optional. GoogleSearchRetrieval tool type. Specialized retrieval tool that is powered by Google search."}, "retrieval": {"$ref": "GoogleCloudAiplatformV1beta1Retrieval", "description": "Optional. Retrieval tool type. System will always execute the provided retrieval tool(s) to get external knowledge to answer the prompt. Retrieval results are presented to the model for generation."}, "urlContext": {"$ref": "GoogleCloudAiplatformV1beta1UrlContext", "description": "Optional. Tool to support URL context retrieval."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1ToolCodeExecution": {"description": "Tool that executes code generated by the model, and automatically returns the result to the model. See also [ExecutableCode]and [CodeExecutionResult] which are input and output to this tool.", "id": "GoogleCloudAiplatformV1beta1ToolCodeExecution", "properties": {}, "type": "object"}, "GoogleCloudAiplatformV1beta1ToolComputerUse": {"description": "Tool to support computer use.", "id": "GoogleCloudAiplatformV1beta1ToolComputerUse", "properties": {"environment": {"description": "Required. The environment being operated.", "enum": ["ENVIRONMENT_UNSPECIFIED", "ENVIRONMENT_BROWSER"], "enumDescriptions": ["Defaults to browser.", "Operates in a web browser."], "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1ToolConfig": {"description": "Tool config. This config is shared for all tools provided in the request.", "id": "GoogleCloudAiplatformV1beta1ToolConfig", "properties": {"functionCallingConfig": {"$ref": "GoogleCloudAiplatformV1beta1FunctionCallingConfig", "description": "Optional. Function calling config."}, "retrievalConfig": {"$ref": "GoogleCloudAiplatformV1beta1RetrievalConfig", "description": "Optional. Retrieval config."}}, "type": "object"}, "GoogleCloudAiplatformV1beta1ToolGoogleSearch": {"description": "GoogleSearch tool type. Tool to support Google Search in Model. Powered by Google.", "id": "GoogleCloudAiplatformV1beta1ToolGoogleSearch", "properties": {}, "type": "object"}, "GoogleCloudAiplatformV1beta1UrlContext": {"description": "Tool to support URL context.", "id": "GoogleCloudAiplatformV1beta1UrlContext", "properties": {}, "type": "object"}, "GoogleCloudAiplatformV1beta1UrlContextMetadata": {"description": "Metadata related to url context retrieval tool.", "id": "GoogleCloudAiplatformV1beta1UrlContextMetadata", "properties": {"urlMetadata": {"description": "Output only. List of url context.", "items": {"$ref": "GoogleCloudAiplatformV1beta1UrlMetadata"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1UrlMetadata": {"description": "Context of the a single url retrieval.", "id": "GoogleCloudAiplatformV1beta1UrlMetadata", "properties": {"retrievedUrl": {"description": "Retrieved url by the tool.", "type": "string"}, "urlRetrievalStatus": {"description": "Status of the url retrieval.", "enum": ["URL_RETRIEVAL_STATUS_UNSPECIFIED", "URL_RETRIEVAL_STATUS_SUCCESS", "URL_RETRIEVAL_STATUS_ERROR"], "enumDescriptions": ["Default value. This value is unused.", "Url retrieval is successful.", "Url retrieval is failed due to error."], "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1VertexAISearch": {"description": "Retrieve from Vertex AI Search datastore or engine for grounding. datastore and engine are mutually exclusive. See https://cloud.google.com/products/agent-builder", "id": "GoogleCloudAiplatformV1beta1VertexAISearch", "properties": {"dataStoreSpecs": {"description": "Specifications that define the specific DataStores to be searched, along with configurations for those data stores. This is only considered for Engines with multiple data stores. It should only be set if engine is used.", "items": {"$ref": "GoogleCloudAiplatformV1beta1VertexAISearchDataStoreSpec"}, "type": "array"}, "datastore": {"description": "Optional. Fully-qualified Vertex AI Search data store resource ID. Format: `projects/{project}/locations/{location}/collections/{collection}/dataStores/{dataStore}`", "type": "string"}, "engine": {"description": "Optional. Fully-qualified Vertex AI Search engine resource ID. Format: `projects/{project}/locations/{location}/collections/{collection}/engines/{engine}`", "type": "string"}, "filter": {"description": "Optional. Filter strings to be passed to the search API.", "type": "string"}, "maxResults": {"description": "Optional. Number of search results to return per query. The default value is 10. The maximumm allowed value is 10.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1VertexAISearchDataStoreSpec": {"description": "Define data stores within engine to filter on in a search call and configurations for those data stores. For more information, see https://cloud.google.com/generative-ai-app-builder/docs/reference/rpc/google.cloud.discoveryengine.v1#datastorespec", "id": "GoogleCloudAiplatformV1beta1VertexAISearchDataStoreSpec", "properties": {"dataStore": {"description": "Full resource name of DataStore, such as Format: `projects/{project}/locations/{location}/collections/{collection}/dataStores/{dataStore}`", "type": "string"}, "filter": {"description": "Optional. Filter specification to filter documents in the data store specified by data_store field. For more information on filtering, see [Filtering](https://cloud.google.com/generative-ai-app-builder/docs/filter-search-metadata)", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1VertexRagStore": {"description": "Retrieve from Vertex RAG Store for grounding.", "id": "GoogleCloudAiplatformV1beta1VertexRagStore", "properties": {"ragCorpora": {"deprecated": true, "description": "Optional. Deprecated. Please use rag_resources instead.", "items": {"type": "string"}, "type": "array"}, "ragResources": {"description": "Optional. The representation of the rag source. It can be used to specify corpus only or ragfiles. Currently only support one corpus or multiple files from one corpus. In the future we may open up multiple corpora support.", "items": {"$ref": "GoogleCloudAiplatformV1beta1VertexRagStoreRagResource"}, "type": "array"}, "ragRetrievalConfig": {"$ref": "GoogleCloudAiplatformV1beta1RagRetrievalConfig", "description": "Optional. The retrieval config for the Rag query."}, "similarityTopK": {"deprecated": true, "description": "Optional. Number of top k results to return from the selected corpora.", "format": "int32", "type": "integer"}, "storeContext": {"description": "Optional. Currently only supported for Gemini Multimodal Live API. In Gemini Multimodal Live API, if `store_context` bool is specified, Gemini will leverage it to automatically memorize the interactions between the client and Gemini, and retrieve context when needed to augment the response generation for users' ongoing and future interactions.", "type": "boolean"}, "vectorDistanceThreshold": {"deprecated": true, "description": "Optional. Only return results with vector distance smaller than the threshold.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1VertexRagStoreRagResource": {"description": "The definition of the Rag resource.", "id": "GoogleCloudAiplatformV1beta1VertexRagStoreRagResource", "properties": {"ragCorpus": {"description": "Optional. RagCorpora resource name. Format: `projects/{project}/locations/{location}/ragCorpora/{rag_corpus}`", "type": "string"}, "ragFileIds": {"description": "Optional. rag_file_id. The files should be in the same rag_corpus set in rag_corpus field.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1VideoMetadata": {"description": "Metadata describes the input video content.", "id": "GoogleCloudAiplatformV1beta1VideoMetadata", "properties": {"endOffset": {"description": "Optional. The end offset of the video.", "format": "google-duration", "type": "string"}, "startOffset": {"description": "Optional. The start offset of the video.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleCloudAiplatformV1beta1VoiceConfig": {"description": "The configuration for the voice to use.", "id": "GoogleCloudAiplatformV1beta1VoiceConfig", "properties": {"prebuiltVoiceConfig": {"$ref": "GoogleCloudAiplatformV1beta1PrebuiltVoiceConfig", "description": "The configuration for the prebuilt voice to use."}}, "type": "object"}, "LatLng": {"description": "An object that represents a latitude/longitude pair. This is expressed as a pair of doubles to represent degrees latitude and degrees longitude. Unless specified otherwise, this object must conform to the WGS84 standard. Values must be within normalized ranges.", "id": "LatLng", "properties": {"latitude": {"description": "The latitude in degrees. It must be in the range [-90.0, +90.0].", "format": "double", "type": "number"}, "longitude": {"description": "The longitude in degrees. It must be in the range [-180.0, +180.0].", "format": "double", "type": "number"}}, "type": "object"}, "ModelOperationMetadata": {"description": "This is returned in the longrunning operations for create/update.", "id": "ModelOperationMetadata", "properties": {"basicOperationStatus": {"enum": ["BASIC_OPERATION_STATUS_UNSPECIFIED", "BASIC_OPERATION_STATUS_UPLOADING", "BASIC_OPERATION_STATUS_VERIFYING"], "enumDescriptions": ["The status is unspecified", "The model file is being uploaded", "The model file is being verified"], "type": "string"}, "name": {"description": "The name of the model we are creating/updating The name must have the form `projects/{project_id}/models/{model_id}`", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Firebase ML API", "version": "v2beta", "version_module": true}