from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool
import urllib.parse
import os

from app.core.config import settings

# Use Supabase PostgreSQL connection from environment variables
DATABASE_URL = settings.DATABASE_URL

if not DATABASE_URL:
    raise ValueError("DATABASE_URL environment variable is required for Supabase connection")

print(f"Connecting to database: {DATABASE_URL.split('@')[0]}@***")

# Create SQLAlchemy engine with PostgreSQL-specific configuration
engine = create_engine(
    DATABASE_URL,
    poolclass=NullPool,  # Use NullPool for Supabase connection pooler
    echo=False,  # Set to True for SQL debugging
    pool_pre_ping=True,  # Verify connections before use
    connect_args={
        "sslmode": "require",  # Require SSL for Supabase
        "application_name": "us_insurance_platform"
    }
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
